{\rtf1\ansi\ansicpg1252\cocoartf2822
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 # Refined MVP Base Asset Requirements List (v2.4)\
\
**Date:** June 12, 2025\
**Version:** 2.4\
**Status:** FINALIZED for MVP Asset Sourcing & Manifestation\
\
This document lists the unique base 3D meshes required for the AI Stylist MVP to visualize the defined mock outfits for personas <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON>. The goal is to maximize reusability of these base meshes through texturing and minor conceptual adaptation by the AI.\
\
---\
\
**I. Avatar**\
1.  `Generic_Humanoid_Avatar_MVP.glb`\
    *   Description: Neutral humanoid, UV unwrapped, medium-poly.\
\
**II. Garments - Torso/Full Body**\
2.  `Base_Long_Flowing_Outerwear.glb`\
3.  `Base_Mesh_Top_Simple.glb`\
4.  `Base_Asymmetrical_Hoodie.glb`\
5.  `Base_Floor_Length_Gown_Simple.glb`\
6.  `Base_Bomber_Jacket.glb`\
7.  `Base_Flowing_Layered_Top.glb`\
8.  `Base_Bodysuit_Full_Body.glb`\
9.  `Base_Capelet_Cropped.glb`\
10. `Base_Jumpsuit_Coverall_Utility.glb`\
\
**III. Garments - Lower Body**\
11. `Base_Pants_Straight_Leg_Versatile.glb`\
12. `Base_Pants_Wide_Leg.glb`\
13. `Base_Skirt_Armored_Structured.glb`\
14. `Base_Skirt_Sarong__Wrap.glb`\
15. `Base_Pants_Slim_Fit.glb`\
\
**IV. Accessories - Headwear/Face**\
16. `Base_Shoulder_Piece_Sculptural_Attachable.glb`\
17. `Base_Collar_High_Stiff_Attachable.glb`\
18. `Base_Visor_Wraparound.glb`\
19. `Base_Face_Mask_Simple_AR_Overlay.glb`\
20. `Base_Halo_Headpiece_Simple_Arc_Ring.glb`\
21. `Base_Hood_Simple_Attachable.glb`\
22. `Base_Draped_Shawl_Rectangular_Plane.glb` (Moved from Body/Limbs in v2.3, was #41)\
\
**V. Accessories - Body/Limbs/Other**\
23. `Base_Belt_Obi_Style_Wide.glb` (Was #22)\
24. `Base_Pendant_Simple_Form.glb` (Was #23)\
25. `Base_Strap_Belt_Thin_Versatile.glb` (Was #24)\
26. `Base_Limb_Adornment_Cylindrical_Wrap_Set.glb` (Was #25 `Base_Arm_Wraps_Cylindrical_Set.glb`, now also covers anklets)\
    *   Description: Simple cylindrical or flattened tube meshes suitable for wrapping arms or, when scaled/positioned and textured, ankles.\
27. `Base_Earrings_Simple_Pair.glb` (Was #26)\
28. `Base_Harness_Chest_Straps_Simple.glb` (Was #27)\
// Item for former #28 (Anklets) is now covered by #26.\
29. `Base_Spikes_Set_Small_Geometric.glb` (Was #29)\
30. `Base_Wrist_Cuff_Simple.glb` (Was #30)\
31. `Base_Ring_Finger_Simple.glb` (Was #31)\
32. `Base_Bangles_Set_Thin_Rings_Wrist.glb` (Was #32)\
33. `Base_Foot_Wraps_Strips_Set.glb` (Was #33)\
34. `Base_Crystal_Form_Attachable_Geometric.glb` (Was #34)\
35. `Base_Modular_Armor_Plates_Set_Geometric.glb` (Was #35)\
36. `Base_Orb_Shape_Small_Floating.glb` (Was #36)\
37. `Base_Curved_Plane_Scroll_Segment.glb` (Was #37)\
\
**VI. Footwear**\
38. `Base_Boots_Simple_Unisex.glb`\
39. `Base_Shoes_Sneaker_Simple_Unisex.glb`\
40. `(Placeholder) Base_Sandals_Simple.glb`\
\
---}