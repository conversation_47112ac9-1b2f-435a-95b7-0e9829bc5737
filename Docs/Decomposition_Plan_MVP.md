Decomposition Plan for Optimization: AI Stylist MVP

Version: 1.0
Date: June 11, 2025
Author(s): Mahkeddah & Resonance

Guiding Principle: Apply MECE (Mutually Exclusive, Collectively Exhaustive) to break down the MVP development into manageable, non-overlapping work packages that cover all necessary aspects. Optimization here refers to optimizing the development process by clearly defining responsibilities and dependencies.

Primary Decomposition Approach: Hybrid of Functional (capabilities) and Structural (system components).

Level 1 Decomposition: Core System Areas

1.  Data Foundation & Management (DFM)
2.  Backend API & AI Core Logic (BAA)
3.  Frontend User Interface & Interaction (FUI)
4.  3D Visualization Engine (TVE)
5.  MVP Assets & Asset Manifest (MAA) - Renamed for clarity from MAI
6.  Overall System Integration, Testing & Demo Prep (SIT)

Level 2 Decomposition: Modules/Tasks within each Core Area

1.  Data Foundation & Management (DFM)
    *   DFM-1: Finalize & Validate JSON Database Schema
        *   Description: Confirm final structure for `personas` objects and `global_keywords_master_list`.
        *   Output: Documented JSON schema overview.
    *   DFM-2: Create & Commit Full JSON Database File
        *   Description: Produce the complete, validated `mvp_stylist_database.json` file containing all persona data and global keywords. Commit to version control.
        *   Output: `mvp_stylist_database.json`. (Effectively completed conceptually)
    *   DFM-3: Design & Create Asset Manifest Structure
        *   Description: Define and create the template for `asset_manifest.json` that maps global keyword tags for visual assets to their file paths.
        *   Output: `asset_manifest_template.json`.

2.  Backend API & AI Core Logic (BAA)
    *   BAA-1: Setup FastAPI Project & Basic Endpoints
        *   Description: Initialize FastAPI project, basic settings, health check endpoint.
        *   Output: Running basic FastAPI application.
    *   BAA-2: Data Loading & Access Module
        *   Description: Implement logic to load/parse `mvp_stylist_database.json` into in-memory Python structures. Provide query functions.
        *   Output: Python module for database access.
    *   BAA-3: Persona Context API Endpoint
        *   Description: API endpoint (e.g., `GET /personas/{persona_id}`) to return selected persona's displayable context.
        *   Output: Functional API endpoint.
    *   BAA-4: AI - Text Prompt Parsing Module (MVP Simple NLP)
        *   Description: Implement keyword spotting/basic pattern matching from user text prompt against global keywords.
        *   Output: Python module for text analysis.
    *   BAA-5: AI - Candidate Tag Pool Generation Logic
        *   Description: Implement logic for AI Core Logic Flow Step C (initial pool, augment, expand by temperature).
        *   Output: Python module for candidate tag generation.
    *   BAA-6: AI - Outfit Structure Assembly Logic
        *   Description: Implement logic for AI Core Logic Flow Step D (slot filling for garments/accessories).
        *   Output: Python module for outfit structure assembly.
    *   BAA-7: AI - Detail Application Logic
        *   Description: Implement logic for AI Core Logic Flow Step E (selecting motifs, textures, colors, vibes).
        *   Output: Python module for detail application.
    *   BAA-8: Main Styling API Endpoint ("Visualize My Style")
        *   Description: API endpoint (e.g., `POST /style_me`) orchestrating AI modules (BAA-4 to BAA-7); returns structured outfit tags.
        *   Output: Functional main AI styling endpoint.
    *   BAA-9: Outfit Rating & Logging API Endpoint
        *   Description: API endpoint (e.g., `POST /rate_outfit`) to receive ratings and log them.
        *   Output: Functional rating endpoint and logging to `ratings_log.jsonl`.
    *   BAA-10: Backend Dockerization (Recommended)
        *   Description: Create `Dockerfile` for the backend.
        *   Output: Dockerized backend application.

3.  Frontend User Interface & Interaction (FUI)
    *   FUI-1: Setup React Project & Basic Layout
        *   Description: Initialize React project, main app layout. Integrate Tailwind CSS.
        *   Output: Running basic React application.
    *   FUI-2: Persona Selection Component
        *   Description: UI component to select personas. Calls backend (BAA-3).
        *   Output: Functional persona selection UI.
    *   FUI-3: Active Persona Context Display Component
        *   Description: UI component to display active persona info.
        *   Output: Persona context display UI.
    *   FUI-4: Styling Input Controls Component
        *   Description: UI components for text prompt, Creativity Temperature, "Visualize My Style" button.
        *   Output: Functional input controls.
    *   FUI-5: API Service Module (Frontend)
        *   Description: JavaScript/TypeScript module for all backend API calls.
        *   Output: API service module.
    *   FUI-6: AI Output Tag Display (Optional Debug/Demo)
        *   Description: Component to display raw outfit tags from AI.
        *   Output: Tag display UI.
    *   FUI-7: Outfit Rating Component
        *   Description: UI component for outfit rating. Calls backend (BAA-9).
        *   Output: Functional rating UI.
    *   FUI-8: UI State Management & Feedback
        *   Description: Implement state management for UI (active selections, loading states). Provide visual feedback for AI processing.
        *   Output: Robust UI state and feedback handling.

4.  3D Visualization Engine (TVE) - Frontend Component
    *   TVE-1: Three.js Scene Setup
        *   Description: Initialize Three.js scene, camera, lighting, renderer in a React component.
        *   Output: Basic empty 3D scene.
    *   TVE-2: Generic Avatar Loading & Display
        *   Description: Load and display the generic 3D avatar model.
        *   Output: Avatar visible in scene.
    *   TVE-3: Asset Mapping & Base Garment/Accessory Loading Logic
        *   Description: Logic to read Asset Manifest (MAA-5) and dynamically load/attach specified base garment/accessory meshes to avatar based on AI tags.
        *   Output: Ability to "dress" avatar with untextured base meshes.
    *   TVE-4: Texture Application Logic
        *   Description: Logic to apply specified textures (mapped via Asset Manifest) to loaded meshes based on AI tags. Basic PBR material setup.
        *   Output: Textured garments on avatar.
    *   TVE-5: Avatar Rotation Control
        *   Description: Mouse/touch controls to rotate the avatar.
        *   Output: Interactive avatar viewing.

5.  MVP Assets & Asset Manifest (MAA)
    *   MAA-1: Source/Acquire Generic Avatar
        *   Description: Execute search/selection for the avatar as per "Shopping List."
        *   Output: `generic_humanoid_avatar_mvp.glb/.fbx` in `frontend/src/assets/avatars/`.
    *   MAA-2: Source/Acquire Base Garment Meshes
        *   Description: Execute search/selection for all listed base garment meshes.
        *   Output: Garment GLB/FBX files in `frontend/src/assets/garments/`.
    *   MAA-3: Source/Acquire Base Accessory Meshes
        *   Description: Execute search/selection for all listed base accessory meshes.
        *   Output: Accessory GLB/FBX files in `frontend/src/assets/accessories/`.
    *   MAA-4: Source/Acquire Base Textures
        *   Description: Execute search/creation for all listed base textures.
        *   Output: Texture image files (PNG/JPG) in `frontend/src/assets/textures/`.
    *   MAA-5: Populate & Commit Asset Manifest
        *   Description: Fill in `asset_manifest.json` (from DFM-3) mapping global tags to file paths of acquired assets (MAA-1 to MAA-4). Commit to version control.
        *   Output: Completed and committed `asset_manifest.json`.

6.  Overall System Integration, Testing & Demo Prep (SIT)
    *   SIT-1: End-to-End Flow Testing & Debugging
        *   Description: Test full user journey. Address bugs.
        *   Output: Stable E2E flow.
    *   SIT-2: Cross-Persona Validation
        *   Description: Ensure AI outputs reflect each persona's style as intended.
        *   Output: Validation of personalization.
    *   SIT-3: NFR Checks (Performance, Stability)
        *   Description: Check against NFRs for response times and stability.
        *   Output: Performance notes.
    *   SIT-4: Demo Script & Walkthrough Preparation
        *   Description: Prepare script and talking points for MVP demo.
        *   Output: MVP Demo Plan.