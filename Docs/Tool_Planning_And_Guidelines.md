Tool Planning and Guidelines: AI Stylist MVP

Version: 1.0 (Revised)
Date: June 11, 2025
Author(s): Mahkeddah & Resonance

A.0. Overarching Success Criteria for MVP Tooling & Solution:

*   SC1 (Demonstrable Core AI Value): The chosen tools and their integration MUST enable the MVP to clearly demonstrate the AI Stylist's core value proposition: generating personalized, context-aware outfit concepts based on deep persona data and user interaction.
*   SC2 (Stable & Polished Demo Experience): The tools MUST support the creation of an MVP that is stable for demonstration, with a responsive and visually appealing user interface that effectively communicates the AI's output.
*   SC3 (Feasible MVP Development): The toolset MUST be achievable for implementation within a reasonable MVP timeframe, prioritizing free and well-supported technologies to minimize overhead.
*   SC4 (Foundation for Iteration): While lean, the tooling choices and architecture SHOULD NOT create significant roadblocks for future enhancements. The JSON database is designed to be migratable.

A.1. Tool Selection Mapping & Justification:

| System Layer         | Component(s)                                       | Selected Tool(s)                      | Justification (Capabilities & Use Cases for MVP Success Criteria)                                                                                                                                                                                                                                                           |
| :------------------- | :------------------------------------------------- | :------------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **User Interface (UI)** | Frontend Web App, 3D Viewer, Styling Controls        | **React, Three.js, Tailwind CSS**       | **React:** Robust component model for interactive UIs (SC2), large ecosystem for rapid development (SC3). Enables clear presentation of AI inputs/outputs. **Three.js:** Essential for dynamic 3D visualization of avatar & garments (SC1, SC2), integrates well with React. **Tailwind CSS:** Utility-first for quick, consistent, polished UI styling (SC2, SC3). |
| **Application Logic** | Backend API Service, Request Orchestration         | **FastAPI (Python), Uvicorn**         | **FastAPI:** High performance for responsive API (SC2), Python for seamless AI integration (SC1), automatic data validation & docs for stable development (SC2, SC3). Manages data flow from UI to AI and back.                                                                                                                            |
| **AI Processing**    | Input Analysis, Outfit Concept Logic, Output Format | **Python 3.8+, Standard Libraries (`json`), spaCy (optional for basic NLP)** | **Python:** Ideal for AI logic development, vast libraries (SC1, SC3). `json` module for direct interaction with our core data. **Core Logic:** Rule-based/algorithmic approach using our curated JSON directly addresses SC1 by making the AI's "knowledge" transparent and effective. spaCy (optional) for slightly more robust prompt parsing if needed (SC1). |
| **Data Storage (MVP)**         | Mock Persona Data, Global Keywords               | **Local JSON File (`mvp_stylist_database.json`)**    | **JSON File:** Simplicity for MVP (SC3), human-readable for easy debugging/iteration, directly consumed by Python AI logic (SC1). Structure designed for future migration (SC4). Enables deep persona context which is key to AI value (SC1).                                                                                            |
| **Infrastructure**   | Development Env, Version Control, Containerization (Backend Rec.) | **VS Code, Git, Docker**     | **VS Code:** Powerful IDE for efficient development (SC3). **Git:** Essential for collaborative development and version tracking (SC3, SC4). **Docker:** (Recommended for backend) Ensures consistent development/demo environment (SC2, SC3), eases future deployment (SC4).                                                               |

A.2. Integration Design (Data Flow Between System Components):

*   **Overall Flow Diagram (Conceptual):**
    `[UI: React (Three.js Viewer, Input Controls)]`
        `↕ (JSON over HTTP via API Service Module)`
    `[Backend API: FastAPI (Endpoints, Data Validation)]`
        `↕ (Python Function Calls)`
    `[AI Core Logic: Python Modules (Prompt Analysis, Tag Pool Gen, Outfit Assembly, Detail App.)]`
        `↕ (Python Data Structures via Data Access Module)`
    `[Data Storage: Local mvp_stylist_database.json]`
    `[UI: React (Rating Component)]` --(JSON over HTTP)--> `[Backend API: FastAPI (Rating Endpoint)]` --> `[Data Storage: ratings_log.jsonl]`

*   **Key Data Exchange Points:**
    1.  **UI to Backend (Styling Request):** JSON payload containing `active_persona_id`, `user_text_prompt`, `creativity_temperature`.
    2.  **Backend to AI Logic:** Parsed Python dictionaries/objects representing the above inputs, plus access to the in-memory parsed full JSON database.
    3.  **AI Logic to Backend:** Python dictionary representing the structured set of `generated_outfit_concept` tags.
    4.  **Backend to UI (Styling Response):** JSON payload containing the `generated_outfit_concept` tags.
    5.  **UI (3D Viewer) to Frontend Assets:** The `generated_outfit_concept` tags are used by frontend logic to look up corresponding file paths in a local asset manifest (mapping tags to `assets/garments/*.glb`, `assets/textures/*.png`, etc.) and load them.
    6.  **UI to Backend (Rating Submission):** JSON payload containing `outfit_identifier` and `rating_value`.

*   **API Integration Points:**
    *   Internal RESTful API exposed by FastAPI. Key Endpoints:
        *   `GET /personas/{persona_id}`: Fetch persona display context.
        *   `POST /style_me`: Submit styling request, receive outfit concept tags.
        *   `POST /rate_outfit`: Submit outfit rating.
        *   `GET /health`: System health check.
*   **Authentication Approach (MVP):**
    *   N/A. No user authentication for the local MVP demo.

A.3. Implementation Planning (High-Level - Refer to Decomposition Plan for Task Details):

*   **Phase 0: Setup & Foundation (~1 week):** Establish project structure, version control, basic F/E-B/E communication, core data file committed, essential 3D assets (avatar, 1-2 garments/textures) for pipeline testing.
*   **Phase 1: Core Data Handling & Basic AI Logic (~2-3 weeks):** Backend loads JSON DB; API for persona context; UI for persona selection; basic text prompt parsing; initial AI tag combination logic (1 persona, 1 temp); API for styling trigger & basic tag return.
*   **Phase 2: 3D Visualization & Iteration (~2-3 weeks):** UI Three.js scene; avatar loading; dynamic loading of base garments/textures based on AI tags; avatar rotation; refine AI logic for all personas/temps; continue sourcing/integrating MVP 3D assets.
*   **Phase 3: Polish, Feedback Loop & Demo Prep (~1-2 weeks):** UI/UX polish; implement outfit rating feature & logging; end-to-end testing; debugging; demo script prep.
*   **Total Estimated MVP Timeline:** Approximately 6-9 weeks.

A.4. Evaluation Approach (Ensuring Solution Effectiveness for MVP):

*   **Functional Requirement Fulfillment:** Systematic testing of each FR.
*   **Non-Functional Requirement Adherence:** Basic performance timing (AI response, UI load); informal usability walkthroughs.
*   **Persona Alignment Testing:** For each persona, run diverse test prompts at different temperatures. Evaluate if AI output (tags & visualization) *feels* appropriate for that persona's style ethos.
*   **Value Proposition Demonstration:** Ensure the demo clearly shows *how* the AI uses persona context for personalized, valuable suggestions.
*   **Feedback Log Analysis (Internal):** Review logged ratings to identify patterns and areas for future AI refinement.
*   **Tooling Performance:** Confirm chosen tools (React, FastAPI, Three.js) perform adequately for the MVP's defined scope on typical development hardware.

A.5. General Development Guidelines:

*   **Version Control (Git):** Commit frequently with clear, descriptive messages. Use a `develop` branch for ongoing work, merging into `main` for stable versions/milestones.
*   **Code Style & Readability:** Adhere to standard style guides (e.g., PEP 8 for Python, Prettier for JavaScript/React). Comment complex logic.
*   **Modularity:** Strive for modular components in both frontend and backend to facilitate easier understanding, testing, and future changes.
*   **Project Structure (Conceptual):**
    ```
    ai_stylist_mvp/
    |-- backend/
    |   |-- app/ (main.py, api/, core/, data/, models/, utils/)
    |   |-- Dockerfile, requirements.txt
    |-- frontend/
    |   |-- public/, src/ (components/, services/, contexts/, assets/)
    |   |-- package.json
    |-- .gitignore, README.md
    ```
*   **Asset Management:** Store all 3D assets (avatar, base garments, accessories, textures) within the `frontend/src/assets/` directory, organized into subfolders (e.g., `avatars/`, `garments/`, `textures/`). Maintain the `asset_manifest.json` diligently.