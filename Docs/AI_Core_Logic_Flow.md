AI Core Logic Flow - MVP: AI Stylist

Version: 1.0
Date: June 11, 2025
Author(s): <PERSON><PERSON><PERSON>dah & Resonance

Purpose: This document describes the conceptual step-by-step logic flow for the AI Stylist MVP. It details how the AI processes inputs (Persona Context, User Text Prompt, Creativity Temperature) by interacting with the JSON knowledge base to generate a structured set of descriptive tags representing a coherent and personalized outfit concept.

Guiding Questions Answered by this Flow:
1.  How is AI in the solution working? (Knowledge-based, rule-driven, combinatorial system)
2.  What data does the AI need to function? (User inputs, JSON database)
3.  What does AI do with that data? (Analysis, filtering, weighted combination, rule application)
4.  What's the output? (Structured set of descriptive tags for an outfit)
5.  What happens if it fails? (Defined error/warning states)

Core Logic Steps:

Step A: Initialization & Context Loading
A1. Receive Inputs: AI module receives `active_persona_id`, `user_text_prompt`, and `creativity_temperature` from the Backend API.
A2. Load Persona Context: AI module accesses the specific `persona_object` (profile, keywords, mock outfit archive, knowledge graph nodes) for the `active_persona_id` from the parsed JSON database.
A3. Load Global Knowledge: AI module accesses the `global_keywords_master_list` from the parsed JSON database.

Step B: Text Prompt Analysis (MVP Simple NLP)
B1. Extract Keywords from Prompt:
    B1.1. Pre-process `user_text_prompt` (e.g., lowercase, basic normalization).
    B1.2. Tokenize the prompt into words and potential phrases.
    B1.3. Match these tokens/phrases against tags present in the `global_keywords_master_list` (categories: `garment_types`, `motifs_patterns`, `texture_shader_tags`, `accessory_types`, `vibe_style_aesthetics`, `color_palettes_defined`).
    B1.4. Assign higher relevance to longer, more specific phrase matches.
    B1.5. Output: A list of `prompt_keyword_tags` with associated relevance scores (simple numeric weight for MVP, e.g., direct match = high, partial match = medium).

Step C: Candidate Tag Pool Generation & Weighting
C1. Initialize Candidate Pool with Persona's "Style DNA":
    C1.1. Add tags from `persona_object.core_style_definition_tags`. Assign high base relevance.
    C1.2. Add tags from `persona_object.primary_cultural_influences_tags`. Assign high base relevance.
    C1.3. Add tags from `persona_object.keywords_persona_specific`. Assign medium-high base relevance.
    C1.4. Add ALL tags (`garment_components_tags`, `motif_tags`, `texture_shader_tags`, `accessory_tags`, `color_palette_tags_applied`, `vibe_tags`) found within the `persona_object.mock_outfit_archive`. Assign relevance based on frequency or recency (simplified for MVP - e.g., medium base relevance for all archive tags).
C2. Integrate Prompt Keywords:
    C2.1. Add `prompt_keyword_tags` (from B1.5) to the Candidate Pool. Significantly boost relevance scores for these tags.
C3. Contextual Expansion & Temperature Influence:
    C3.1. For each tag currently in the Candidate Pool (especially vibe and cultural tags):
        *   If `creativity_temperature` is "Subtle Remix": Strongly bias towards tags already present in C1.1-C1.4, especially those from the `mock_outfit_archive`. Limit new tag introduction.
        *   If `creativity_temperature` is "Signature Style": Moderately expand by finding directly related tags in `global_keywords_master_list` (e.g., if "Asian_Futurist" is in the pool, consider adding more specific Asian-themed garment or motif tags that align with other active keywords). Utilize `persona_object.ai_knowledge_graph_nodes` for simple, direct relationship expansions.
        *   If `creativity_temperature` is "Creative Wildcard": Allow more liberal expansion. Consider a small probability of introducing a less common but thematically compatible tag from `global_keywords_master_list`, ensuring it can still be filtered/styled through the persona's core color palettes and dominant vibes. *Constraint: Avoid purely random additions.*
    C3.2. Adjust relevance scores in the Candidate Pool based on these expansions and temperature settings.

Step D: Outfit Structure Assembly (Rule-Based Slot Filling)
D1. Define Core Outfit Slots to Fill:
    *   `slot_top_garment`: (e.g., shirt, bodice)
    *   `slot_bottom_garment`: (e.g., pants, skirt)
    *   `slot_outerwear_garment`: (optional)
    *   `slot_footwear_mvp`: (optional, very simple for MVP, e.g., one generic `footwear_boot_generic_basic` tag if applicable)
    *   `slot_main_accessory_1`: (optional)
    *   `slot_headwear_accessory_mvp`: (optional)
D2. Select Tags for Core Garment Slots:
    D2.1. For each slot (e.g., `slot_top_garment`):
        *   Filter the Candidate Pool for `garment_types` tags suitable for that slot.
        *   Select the highest relevance tag that also has good coherence with already selected tags (if any) and `prompt_keyword_tags`.
        *   If multiple high-relevance options, a degree of randomness influenced by "Temperature" can select one (e.g., High Temp might pick a slightly less common but still relevant option).
        *   Ensure basic garment logic (e.g., if a "Bodysuit" tag is selected, it might fill both top and bottom slots conceptually).
D3. Output: A list of primary `selected_garment_tags`.

Step E: Detail Application (Motifs, Textures, Colors, Accessories, Vibes)
E1. Apply Details to Selected Garments:
    E1.1. For each `selected_garment_tag` from D3:
        *   Filter Candidate Pool for relevant `motif_tags` and `texture_shader_tags`.
        *   Select 1-2 motif tags and 1-2 texture/shader tags based on:
            *   Relevance scores.
            *   Coherence with the garment's cultural origin hint (if any).
            *   Coherence with active `vibe_tags` from prompt or persona.
            *   Temperature influence (Subtle = stick to known combos from archive; Wildcard = try novel but plausible pairings).
E2. Select Overall Color Palette:
    E2.1. Prioritize any color-related `prompt_keyword_tags`.
    E2.2. Otherwise, select one `color_palettes_defined` tag from the Candidate Pool that aligns with `persona_object.color_palette_preference_tags`.
E3. Select Accessories:
    E3.1. For `slot_main_accessory_1` and `slot_headwear_accessory_mvp` (and any other optional accessory slots defined):
        *   Filter Candidate Pool for relevant `accessory_types` tags.
        *   Select based on relevance, coherence with selected garments/vibes, and temperature.
E4. Finalize Dominant Vibe Tags:
    E4.1. From the Candidate Pool (which includes persona's core vibes and prompt vibes), select 2-4 most dominant and descriptive `vibe_style_aesthetics` tags that characterize the generated ensemble.

Step F: Output Formatting
F1. Assemble Final Tag Set: Collate all selected tags from D3 and E1-E4 into a structured output object.
    *   `garment_components_tags`: (from D3 and potentially accessory garments)
    *   `motif_tags`: (from E1.1)
    *   `texture_shader_tags`: (from E1.1)
    *   `accessory_tags`: (from E3.1)
    *   `color_palette_tags_applied`: (from E2.1)
    *   `vibe_tags`: (from E4.1)
F2. Include Contextual Metadata: Add `persona_id`, `prompt_used`, `temperature_used` to the output object for reference and logging.

Step G: Error Handling & Fallbacks (MVP)
G1. Failure to Populate Core Slots: If Step D2 cannot confidently select tags for essential garment slots (e.g., no suitable top/bottom found based on highly obscure prompt and no relevant persona history).
    *   Action: AI module returns an error state/message indicating difficulty in forming a base outfit structure.
G2. Insufficient Coherent Details: If core garments are selected, but Step E struggles to find sufficiently relevant/coherent detail tags (motifs, textures, accessories).
    *   Action (MVP): AI module may return the outfit with fewer detail tags, or a warning indicating sparse detailing. Prioritize returning *something* if a basic structure is viable.
G3. General Processing Exception:
    *   Action: AI module catches unexpected internal errors, logs them (for developer review), and returns a generic error state/message.