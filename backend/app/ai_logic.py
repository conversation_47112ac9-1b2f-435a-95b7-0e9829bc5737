from typing import List, Dict, Any
from .data.data_loader import get_database

def recommend_outfits(persona_id: str, style_tags: List[str]) -> List[Dict[str, Any]]:
    """
    Recommends outfits for a given persona based on style tags.

    Args:
        persona_id (str): The ID of the persona to recommend outfits for.
        style_tags (List[str]): A list of style tags to filter outfits.

    Returns:
        List[Dict[str, Any]]: A list of recommended outfits matching the style tags.
    """
    db = get_database()
    persona = next((p for p in db.get("personas", []) if p["id"] == persona_id), None)

    if not persona:
        return []

    recommended_outfits = []
    for outfit in persona.get("mock_outfit_archive", []):
        if any(tag in outfit.get("vibe_tags", []) for tag in style_tags):
            recommended_outfits.append(outfit)

    return recommended_outfits

def get_persona_keywords(persona_id: str) -> List[str]:
    """
    Retrieves the keywords associated with a given persona.

    Args:
        persona_id (str): The ID of the persona.

    Returns:
        List[str]: A list of keywords associated with the persona.
    """
    db = get_database()
    persona = next((p for p in db.get("personas", []) if p["id"] == persona_id), None)

    if not persona:
        return []

    return persona.get("keywords_persona_specific", [])

import random
from typing import Dict, Any, List, Set

# Import the data loading functions we created earlier
# This allows the AI logic to access the "brain" (our JSON database)
from .data.data_loader import get_database, get_persona_by_id

# --- Constants for AI Logic ---
# These weights help us prioritize different sources of tags.
# Higher weight means more important.
WEIGHTS = {
    "PROMPT_DIRECT_MATCH": 10.0,
    "PERSONA_CORE_STYLE": 5.0,
    "PERSONA_CULTURAL_INFLUENCE": 4.0,
    "PERSONA_ARCHIVE_VIBE": 3.0,
    "PERSONA_ARCHIVE_GARMENT": 2.0,
    "DEFAULT": 1.0,
}

# --- AI Core Logic Orchestration ---

def generate_outfit_concept(persona_id: str, user_prompt: str, temperature: str) -> Dict[str, Any]:
    """
    Main orchestrator function for the AI Stylist.
    Takes user inputs and generates a structured outfit concept.
    Follows the AI Core Logic Flow.
    """
    print(f"🧠 AI: Starting outfit generation for Persona: {persona_id}, Temp: {temperature}")
    
    # Step A: Context is already loaded on app startup. We just need to get it.
    persona_context = get_persona_by_id(persona_id)
    if not persona_context:
        return {"error": f"Persona with ID '{persona_id}' not found."}

    global_keywords = get_database().get("global_keywords_master_list", {})

    # Step B: Text Prompt Analysis
    prompt_keyword_tags = parse_prompt(user_prompt, global_keywords)
    print(f"   - AI Step B: Parsed prompt keywords: {prompt_keyword_tags}")

    # Step C: Candidate Tag Pool Generation & Weighting
    candidate_pool = generate_candidate_pool(persona_context, prompt_keyword_tags, global_keywords, temperature)
    print(f"   - AI Step C: Generated candidate pool with {len(candidate_pool)} weighted tags.")

    # Step D: Outfit Structure Assembly
    selected_garments, candidate_pool = assemble_outfit(candidate_pool, persona_context, temperature)
    if not selected_garments:
        return {"error": "AI could not assemble a coherent base outfit from the prompt and persona context."}
    print(f"   - AI Step D: Assembled base garments: {[g['tag'] for g in selected_garments]}")

    # Step E: Detail Application
    final_outfit_tags = apply_details(selected_garments, candidate_pool, persona_context, temperature)
    print(f"   - AI Step E: Applied details. Finalizing concept.")

    # Step F: Output Formatting
    final_outfit_tags["metadata"] = {
        "persona_id": persona_id,
        "user_prompt": user_prompt,
        "temperature_used": temperature
    }

    print("✅ AI: Outfit concept generation complete.")
    return final_outfit_tags


# --- Helper Functions for Each Step ---

def parse_prompt(user_prompt: str, global_keywords: Dict[str, Any]) -> Set[str]:
    """
    MVP Simple NLP: Extracts keywords from the prompt by matching against the global list.
    Returns a set of unique tag strings found in the prompt.
    """
    # For MVP, this is a very simple keyword spotter.
    # In a future version, this would use more advanced NLP.
    prompt_lower = user_prompt.lower()
    found_tags = set()
    
    # We check all categories of keywords
    all_tags = []
    for category in global_keywords.values():
        if isinstance(category, list):
            for item in category:
                if isinstance(item, dict) and "tag" in item:
                    all_tags.append(item["tag"])

    for tag in all_tags:
        # Check if the tag (e.g., "Asymmetrical_Hoodie_Layered") as a whole phrase
        # is in the prompt, after making the prompt safe for matching.
        if tag.lower().replace('_', ' ') in prompt_lower:
            found_tags.add(tag)
            
    return found_tags


def generate_candidate_pool(persona_context: Dict[str, Any], prompt_keyword_tags: Set[str], global_keywords: Dict[str, Any], temperature: str) -> Dict[str, float]:
    """
    Generates a weighted pool of candidate tags based on persona, prompt, and temperature.
    Returns a dictionary of {tag: relevance_score}.
    """
    pool = {}

    # C1: Add tags from Persona's "Style DNA"
    for tag in persona_context.get("core_style_definition_tags", []):
        pool[tag] = WEIGHTS["PERSONA_CORE_STYLE"]
    for tag in persona_context.get("primary_cultural_influences_tags", []):
        pool[tag] = WEIGHTS["PERSONA_CULTURAL_INFLUENCE"]
    
    # Add tags from the persona's past work (mock outfit archive)
    for outfit in persona_context.get("mock_outfit_archive", []):
        for tag_category in outfit.values():
            if isinstance(tag_category, list):
                for tag in tag_category:
                    # Give higher weight to vibes, less to specific garments from archive
                    weight = WEIGHTS["PERSONA_ARCHIVE_VIBE"] if "vibe" in tag.lower() else WEIGHTS["PERSONA_ARCHIVE_GARMENT"]
                    pool[tag] = max(pool.get(tag, 0), weight) # Keep the highest weight if tag already exists

    # C2: Integrate Prompt Keywords with the highest weight
    for tag in prompt_keyword_tags:
        pool[tag] = WEIGHTS["PROMPT_DIRECT_MATCH"]
        
    # C3: Contextual Expansion & Temperature Influence (Simplified for MVD)
    # A real implementation would be more complex. For MVD, temperature will mostly affect
    # the *selection* process in the later functions. Here, we can just log it.
    print(f"   - Temperature '{temperature}' will influence selections from the generated pool.")

    return pool


def assemble_outfit(candidate_pool: Dict[str, float], persona_context: Dict[str, Any], temperature: str) -> (List[Dict[str, Any]], Dict[str, float]):
    """
    Rule-Based Slot Filling: Selects core garments to form the outfit structure.
    This is the most critical and complex part of the MVP AI logic.
    For this prompt, we will create a clear skeleton to be filled in.
    """
    print("   - assemble_outfit(): Starting garment selection...")

    # --- This is where the core "thinking" happens for the MVD ---
    # We need to define rules to pick 1 top, 1 bottom (or a full-body item).

    # 1. Get all possible garments from the global list
    all_garments = get_database().get("global_keywords_master_list", {}).get("garment_types", [])

    # 2. Score possible garments based on their presence in our candidate_pool
    scored_garments = []
    for garment in all_garments:
        tag = garment.get("tag")
        if tag in candidate_pool:
            scored_garments.append({"tag": tag, "score": candidate_pool[tag], "base_type": garment.get("base_type")})

    # 3. Sort by score to find the most relevant garments
    scored_garments.sort(key=lambda x: x["score"], reverse=True)

    # 4. Implement simple slot-filling logic
    selected_garments = []
    filled_slots = set()

    # Prioritize full-body garments if they are highly relevant
    for garment in scored_garments:
        if garment["base_type"] in ["Bodysuit", "Suit_FullBody", "Gown_Dress", "Jumpsuit_Coverall_Utility"]:
            selected_garments.append(garment)
            filled_slots.update(["top", "bottom"])
            break # We have our core garment, stop here

    # If no full-body garment was chosen, look for a top and a bottom
    if "top" not in filled_slots:
        for garment in scored_garments:
            if garment["base_type"] in ["Top", "Tunic_Top", "Vest_Outerwear"] and "top" not in filled_slots:
                selected_garments.append(garment)
                filled_slots.add("top")

    if "bottom" not in filled_slots:
        for garment in scored_garments:
            if garment["base_type"] in ["Pants", "Pants_Leggings", "Skirt", "Skirt_LowerBody_Wrap"] and "bottom" not in filled_slots:
                selected_garments.append(garment)
                filled_slots.add("bottom")

    # Implement Diallo Logic: Prioritize specific garments for Dr. Diallo's regal style
    if persona_context.get("id") == "dr_diallo_002":
        if "Neo_Ancestral_Glam_Diallo" in candidate_pool and candidate_pool["Neo_Ancestral_Glam_Diallo"] > 5.0:
            for garment in scored_garments:
                if garment["tag"] in ["Base_Floor_Length_Gown_Simple", "Tech_Enhanced_Agbada_Flowing_Unisex"]:
                    selected_garments.append(garment)
                    filled_slots.update(["top", "bottom"])
                    print(f"   - Prioritized '{garment['tag']}' for Dr. Diallo's regal style.")
                    break

    # Implement Maya Logic: Add secondary outerwear for her signature layered look
    if persona_context.get("id") == "maya_001":
        if ("TripHop_Vibe" in candidate_pool and candidate_pool["TripHop_Vibe"] > 5.0) or \
           ("Atmospheric_Layering_Preference" in candidate_pool and candidate_pool["Atmospheric_Layering_Preference"] > 5.0):
            if temperature != "Subtle Remix":
                for garment in scored_garments:
                    if garment["tag"] in ["Base_Capelet_Cropped", "Base_Bomber_Jacket"]:
                        selected_garments.append(garment)
                        print(f"   - Added secondary outerwear '{garment['tag']}' for Maya's layered look.")
                        break

    # Remove selected garment tags from the pool so they aren't re-selected as details
    for garment in selected_garments:
        if garment["tag"] in candidate_pool:
            del candidate_pool[garment["tag"]]

    return selected_garments, candidate_pool


def apply_details(selected_garments: List[Dict[str, Any]], candidate_pool: Dict[str, float], persona_context: Dict[str, Any], temperature: str) -> Dict[str, Any]:
    """
    Applies detail tags (motifs, textures, accessories, etc.) to the structured outfit.
    Another skeleton function to be filled in.
    """
    print("   - apply_details(): Applying details to selected garments...")

    final_tags = {
        "garment_components_tags": [g["tag"] for g in selected_garments],
        "motif_tags": [],
        "texture_shader_tags": [],
        "accessory_tags": [],
        "color_palette_tags_applied": [],
        "vibe_tags": []
    }

    # --- Another core "thinking" area for MVD ---

    # 1. Get all candidate tags for each category, sorted by relevance
    db = get_database()
    global_keywords = db.get("global_keywords_master_list", {})

    # Get all tags from each category and filter by what's in our candidate pool
    motif_tags = [item.get("tag") for item in global_keywords.get("motifs_patterns", []) if isinstance(item, dict) and item.get("tag")]
    texture_tags = [item.get("tag") for item in global_keywords.get("textures_shaders", []) if isinstance(item, dict) and item.get("tag")]
    accessory_tags = [item.get("tag") for item in global_keywords.get("accessory_types", []) if isinstance(item, dict) and item.get("tag")]
    color_tags = [item.get("tag") for item in global_keywords.get("color_palettes_defined", []) if isinstance(item, dict) and item.get("tag")]
    vibe_tags = [item.get("tag") for item in global_keywords.get("vibe_style_aesthetics", []) if isinstance(item, dict) and item.get("tag")]

    motifs = sorted([tag for tag in candidate_pool if tag in motif_tags], key=lambda t: candidate_pool[t], reverse=True)
    textures = sorted([tag for tag in candidate_pool if tag in texture_tags], key=lambda t: candidate_pool[t], reverse=True)
    accessories = sorted([tag for tag in candidate_pool if tag in accessory_tags], key=lambda t: candidate_pool[t], reverse=True)
    colors = sorted([tag for tag in candidate_pool if tag in color_tags], key=lambda t: candidate_pool[t], reverse=True)
    vibes = sorted([tag for tag in candidate_pool if tag in vibe_tags], key=lambda t: candidate_pool[t], reverse=True)
    
    # 2. Apply a few of the top-scoring tags from each category
    # For MVD, we can keep this simple. Temperature can add randomness here.
    if motifs:
        final_tags["motif_tags"].append(motifs) # Add the most relevant motif
    if textures:
        final_tags["texture_shader_tags"].append(textures)
    if accessories:
        final_tags["accessory_tags"].append(accessories) # Add one main accessory
    if colors:
        final_tags["color_palette_tags_applied"].append(colors)
    if vibes:
        final_tags["vibe_tags"] = vibes[:3] # Add the top 3 most relevant vibes

    # TODO / For you and Copilot to expand:
    # - Add logic to select *more* details based on temperature.
    # - Add coherence checks (e.g., don't pair a "Minimalist" vibe with tons of accessories).
    
    # Add logic for 'Creative Wildcard' temperature
    if temperature == 'Creative Wildcard':
        # Check if 'Base_Capelet_Cropped' exists in the candidate pool and has a high score
        capelet_tag = 'Base_Capelet_Cropped'
        if capelet_tag in candidate_pool and candidate_pool[capelet_tag] > 5.0:  # Example threshold score
            final_tags["garment_components_tags"].append(capelet_tag)
            print(f"   - Added '{capelet_tag}' due to 'Creative Wildcard' temperature.")

    # Add a second accessory if the temperature is not 'Subtle Remix'
    if temperature != 'Subtle Remix':
        if len(accessories) > 1:
            final_tags["accessory_tags"].append(accessories[1])  # Add the second highest scoring accessory
            print(f"   - Added second accessory '{accessories[1]}' as temperature is not 'Subtle Remix'.")

    # Add coherence checks to ensure compatibility between vibes and accessories
    if "Minimalist" in final_tags["vibe_tags"]:
        # If the vibe is 'Minimalist', limit the number of accessories to 1
        if len(final_tags["accessory_tags"]) > 1:
            final_tags["accessory_tags"] = final_tags["accessory_tags"][:1]
            print("   - Coherence check: Limited accessories to 1 due to 'Minimalist' vibe.")

    # TODO (Texture Coherence): For the selected garments, cross-reference their assigned cultural or vibe hints with texture tags.
    # For example, if a garment is tagged with 'Bio_Mythic_Kai', prioritize selecting texture tags like 'Digital_Bark_Cloth_Shader_Kai'
    # or 'Bioluminescent_Mycelial_Network_Shader' from the candidate pool.

    # Implement Texture Coherence: Apply textures based on cultural or vibe hints
    for garment in selected_garments:
        garment_tag = garment["tag"]
        if garment_tag == "Bio_Mythic_Kai":
            # Prioritize specific textures for 'Bio_Mythic_Kai'
            texture_options = ["Digital_Bark_Cloth_Shader_Kai", "Bioluminescent_Mycelial_Network_Shader"]
            for texture in texture_options:
                if texture in candidate_pool:
                    final_tags["texture_shader_tags"].append(texture)
                    print(f"   - Added texture '{texture}' for garment '{garment_tag}'.")
                    break

    # Implement Accessory Personalization: Select accessories based on the active persona
    if persona_context.get("id") == "dr_diallo_002":
        # Favor statement pieces for Dr. Diallo
        preferred_accessories = ["Statement_Neckpiece_Tech_Tribal_Diallo", "AR_Power_Gauntlets_Glyph_Activated_Diallo"]
        for accessory in preferred_accessories:
            if accessory in candidate_pool:
                final_tags["accessory_tags"].append(accessory)
                print(f"   - Added '{accessory}' for Dr. Diallo's personalized accessory selection.")
                break

    if persona_context.get("id") == "maya_001":
        # Favor tech-mysticism items for Maya
        preferred_accessories = ["Neural_Halo_Headpiece_Gold", "Holographic_Bangles_AR_Runes"]
        for accessory in preferred_accessories:
            if accessory in candidate_pool:
                final_tags["accessory_tags"].append(accessory)
                print(f"   - Added '{accessory}' for Maya's personalized accessory selection.")
                break

    # Implement Wildcard Details: Add a surprising detail for 'Creative Wildcard' temperature
    if temperature == 'Creative Wildcard':
        secondary_influences = persona_context.get("primary_cultural_influences_tags", [])[1:]  # Exclude the primary influence
        for influence in secondary_influences:
            # Check for a motif, texture, or accessory related to the secondary influence
            wildcard_options = [
                "Lotus_Circuit_Mandala_Asian_Spirituality",  # Example motif
                "Holographic_Feather_Texture_Māori_Conceptual",  # Example texture
                "Abstract_Symbolic_Headdress_Digital_Aus"  # Example accessory
            ]
            for option in wildcard_options:
                if option in candidate_pool:
                    if "motif" in option:
                        final_tags["motif_tags"].append(option)
                    elif "texture" in option:
                        final_tags["texture_shader_tags"].append(option)
                    elif "accessory" in option:
                        final_tags["accessory_tags"].append(option)
                    print(f"   - Added wildcard detail '{option}' for 'Creative Wildcard' temperature.")
                    break

    return final_tags