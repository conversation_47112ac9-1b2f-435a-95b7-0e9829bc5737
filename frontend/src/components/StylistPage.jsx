import React from 'react';
import StylingInputs from './StylingInputs';
import ThreeScene from './ThreeScene';

const StylistPage = ({ 
  selectedPersona, 
  outfitTags, 
  onGenerateOutfit, 
  onBackToLanding, 
  loading, 
  error 
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBackToLanding}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>Back to Personas</span>
              </button>
              
              <div className="h-6 w-px bg-gray-300" />
              
              <div className="flex items-center space-x-3">
                <div 
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold text-sm"
                  style={{ backgroundColor: selectedPersona?.color || '#4F46E5' }}
                >
                  {selectedPersona?.name?.charAt(0)}
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">{selectedPersona?.name}</h1>
                  <p className="text-sm text-gray-600">{selectedPersona?.description}</p>
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <h2 className="text-lg font-semibold text-gray-900">AI Stylist</h2>
              <p className="text-sm text-gray-600">Digital Fashion Co-Creation</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Controls */}
          <div className="space-y-6">
            {/* Styling Controls */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Style Configuration</h3>
              <StylingInputs
                selectedPersona={selectedPersona}
                onGenerateOutfit={onGenerateOutfit}
                loading={loading}
                disabled={!selectedPersona}
              />
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
              </div>
            )}

            {/* AI Generated Tags Display */}
            {outfitTags && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Generated Tags</h3>
                <div className="space-y-3">
                  {Object.entries(outfitTags).map(([key, value]) => {
                    if (key === 'metadata') return null;
                    return (
                      <div key={key} className="border-b border-gray-100 pb-3 last:border-b-0 last:pb-0">
                        <h4 className="text-sm font-medium text-gray-700 mb-2 capitalize">
                          {key.replace(/_/g, ' ')}
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {Array.isArray(value) ? (
                            value.map((item, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium"
                              >
                                {item}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-600 text-sm">{value}</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">How to Use</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Select gender and adjust creativity temperature</li>
                <li>• Describe your desired outfit style</li>
                <li>• Click "Generate Outfit" to create AI styling</li>
                <li>• View your 3D avatar with the generated outfit</li>
              </ul>
            </div>
          </div>

          {/* Right Panel - 3D Avatar Display */}
          <div className="lg:sticky lg:top-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* 3D Viewer Header */}
              <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">3D Avatar</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span>Drag to rotate • Scroll to zoom</span>
                  </div>
                </div>
              </div>

              {/* 3D Viewer Content */}
              <div className="relative bg-gradient-to-br from-gray-100 to-gray-200" style={{ height: '800px' }}>
                <ThreeScene
                  outfitTags={outfitTags}
                  loading={loading}
                />

                {/* Loading State */}
                {loading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600 font-medium">Generating your outfit...</p>
                      <p className="text-gray-500 text-sm">This may take a few seconds</p>
                    </div>
                  </div>
                )}

                {/* Empty State */}
                {!outfitTags && !loading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <svg className="w-20 h-20 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <h4 className="text-lg font-medium text-gray-700 mb-2">Ready for Styling</h4>
                      <p className="text-gray-500">Configure your style preferences and generate an outfit</p>
                      <p className="text-gray-400 text-sm mt-2">Your 3D avatar will appear here</p>
                    </div>
                  </div>
                )}
              </div>

              {/* 3D Viewer Footer */}
              <div className="bg-gray-50 border-t border-gray-200 px-6 py-3">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>Interactive 3D Visualization</span>
                  {outfitTags && (
                    <span className="text-green-600 font-medium">✓ Outfit Generated</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default StylistPage;
