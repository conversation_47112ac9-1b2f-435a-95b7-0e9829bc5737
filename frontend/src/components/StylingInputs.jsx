import React, { useState } from 'react';

const StylingInputs = ({ 
  selectedPersona, 
  onGenerateOutfit, 
  loading, 
  disabled 
}) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [temperature, setTemperature] = useState('Signature Style');

  const temperatureOptions = [
    {
      value: 'Subtle Remix',
      label: 'Subtle Remix',
      description: 'Conservative variations on the persona\'s signature style',
      icon: '🎯'
    },
    {
      value: 'Signature Style',
      label: 'Signature Style',
      description: 'Classic interpretation of the persona\'s aesthetic DNA',
      icon: '✨'
    },
    {
      value: 'Creative Wildcard',
      label: 'Creative Wildcard',
      description: 'Bold experimental fusion with unexpected elements',
      icon: '🚀'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedPersona || !userPrompt.trim()) return;
    
    onGenerateOutfit({
      persona_id: selectedPersona.id,
      user_prompt: userPrompt.trim(),
      temperature: temperature
    });
  };

  const promptSuggestions = [
    'Create a futuristic outfit with layered textures',
    'Design something for a lunar tide ritual ceremony',
    'Make an outfit for a digital fashion runway show',
    'Create a look for exploring virtual worlds',
    'Design something that blends ancient and future aesthetics'
  ];

  return (
    <div className="w-full max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Active Persona Display */}
        {selectedPersona && (
          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                <span className="text-lg font-bold text-white">
                  {selectedPersona.name.charAt(0)}
                </span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">
                  Styling for {selectedPersona.name}
                </h3>
                <p className="text-sm text-gray-300">
                  {selectedPersona.pronouns} • {selectedPersona.profession?.split(',')[0]}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Text Prompt Input */}
        <div className="space-y-3">
          <label htmlFor="prompt" className="block text-lg font-medium text-white">
            Describe Your Vision
          </label>
          <textarea
            id="prompt"
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            placeholder="Describe the outfit you want to create..."
            className="w-full h-32 px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent resize-none"
            disabled={disabled || !selectedPersona}
          />
          
          {/* Prompt Suggestions */}
          <div className="space-y-2">
            <p className="text-sm text-gray-400">Try these suggestions:</p>
            <div className="flex flex-wrap gap-2">
              {promptSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setUserPrompt(suggestion)}
                  className="px-3 py-1 text-xs bg-gray-700/50 text-gray-300 rounded-full hover:bg-gray-600/50 transition-colors border border-gray-600/50"
                  disabled={disabled || !selectedPersona}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Creativity Temperature */}
        <div className="space-y-3">
          <label className="block text-lg font-medium text-white">
            Creativity Temperature
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {temperatureOptions.map((option) => (
              <label
                key={option.value}
                className={`
                  relative cursor-pointer transition-all duration-200
                  ${temperature === option.value
                    ? 'ring-2 ring-purple-400 bg-purple-500/20'
                    : 'hover:bg-gray-700/30'
                  }
                  bg-gray-800/30 border border-gray-600/50 rounded-lg p-4
                  ${disabled || !selectedPersona ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <input
                  type="radio"
                  name="temperature"
                  value={option.value}
                  checked={temperature === option.value}
                  onChange={(e) => setTemperature(e.target.value)}
                  className="sr-only"
                  disabled={disabled || !selectedPersona}
                />
                <div className="text-center">
                  <div className="text-2xl mb-2">{option.icon}</div>
                  <div className="font-medium text-white mb-1">{option.label}</div>
                  <div className="text-xs text-gray-400">{option.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Generate Button */}
        <button
          type="submit"
          disabled={disabled || !selectedPersona || !userPrompt.trim() || loading}
          className={`
            w-full py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200
            ${disabled || !selectedPersona || !userPrompt.trim()
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : loading
              ? 'bg-purple-600 text-white cursor-wait'
              : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25'
            }
          `}
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <span>AI is Creating Your Style...</span>
            </div>
          ) : (
            'Visualize My Style ✨'
          )}
        </button>
      </form>
    </div>
  );
};

export default StylingInputs;
