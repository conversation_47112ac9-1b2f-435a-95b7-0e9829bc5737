import React, { useState } from 'react';

const StylingInputs = ({
  selectedPersona,
  onGenerateOutfit,
  loading,
  disabled
}) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [temperature, setTemperature] = useState('Signature Style');
  const [gender, setGender] = useState('female');

  const temperatureOptions = [
    {
      value: 'Subtle Remix',
      label: 'Subtle Remix',
      description: 'Conservative variations on the persona\'s signature style',
      icon: '🎯'
    },
    {
      value: 'Signature Style',
      label: 'Signature Style',
      description: 'Classic interpretation of the persona\'s aesthetic DNA',
      icon: '✨'
    },
    {
      value: 'Creative Wildcard',
      label: 'Creative Wildcard',
      description: 'Bold experimental fusion with unexpected elements',
      icon: '🚀'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedPersona || !userPrompt.trim()) return;

    onGenerateOutfit({
      persona_id: selectedPersona.id,
      user_prompt: userPrompt.trim(),
      temperature: temperature,
      gender: gender
    });
  };

  const promptSuggestions = [
    'Create a futuristic outfit with layered textures',
    'Design something for a lunar tide ritual ceremony',
    'Make an outfit for a digital fashion runway show',
    'Create a look for exploring virtual worlds',
    'Design something that blends ancient and future aesthetics'
  ];

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Gender Selection */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            Avatar Gender
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="gender"
                value="female"
                checked={gender === 'female'}
                onChange={(e) => setGender(e.target.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                disabled={disabled}
              />
              <span className="ml-2 text-sm text-gray-700">Female</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="gender"
                value="male"
                checked={gender === 'male'}
                onChange={(e) => setGender(e.target.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                disabled={disabled}
              />
              <span className="ml-2 text-sm text-gray-700">Male</span>
            </label>
          </div>
        </div>

        {/* Text Prompt Input */}
        <div className="space-y-3">
          <label htmlFor="prompt" className="block text-sm font-medium text-gray-700">
            Outfit Description
          </label>
          <textarea
            id="prompt"
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            placeholder="Describe the outfit you want to create..."
            className="w-full h-32 px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            disabled={disabled || !selectedPersona}
          />

          {/* Prompt Suggestions */}
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Try these suggestions:</p>
            <div className="flex flex-wrap gap-2">
              {promptSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setUserPrompt(suggestion)}
                  className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors border border-gray-300"
                  disabled={disabled || !selectedPersona}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Creativity Temperature */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            Creativity Level
          </label>
          <div className="grid grid-cols-1 gap-3">
            {temperatureOptions.map((option) => (
              <label
                key={option.value}
                className={`
                  relative cursor-pointer transition-all duration-200 flex items-center p-4 rounded-lg border
                  ${temperature === option.value
                    ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                    : 'hover:bg-gray-50 border-gray-300'
                  }
                  ${disabled || !selectedPersona ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <input
                  type="radio"
                  name="temperature"
                  value={option.value}
                  checked={temperature === option.value}
                  onChange={(e) => setTemperature(e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mr-4"
                  disabled={disabled || !selectedPersona}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{option.icon}</span>
                    <span className="font-medium text-gray-900">{option.label}</span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Generate Button */}
        <button
          type="submit"
          disabled={disabled || !selectedPersona || !userPrompt.trim() || loading}
          className={`
            w-full py-3 px-6 rounded-lg font-medium transition-all duration-200
            ${disabled || !selectedPersona || !userPrompt.trim()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : loading
              ? 'bg-blue-600 text-white cursor-wait'
              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md'
            }
          `}
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Generating Outfit...</span>
            </div>
          ) : (
            'Generate Outfit'
          )}
        </button>
      </form>
    </div>
  );
};

export default StylingInputs;
