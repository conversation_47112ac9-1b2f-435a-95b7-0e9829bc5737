import React, { useState } from 'react';

const StylingInputs = ({
  selectedPersona,
  onGenerateOutfit,
  loading,
  disabled
}) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [temperature, setTemperature] = useState('Signature Style');
  const [gender, setGender] = useState('female');

  const temperatureOptions = [
    {
      value: 'Subtle Remix',
      label: 'Subtle Remix',
      description: 'Conservative variations on the persona\'s signature style',
      icon: '🎯'
    },
    {
      value: 'Signature Style',
      label: 'Signature Style',
      description: 'Classic interpretation of the persona\'s aesthetic DNA',
      icon: '✨'
    },
    {
      value: 'Creative Wildcard',
      label: 'Creative Wildcard',
      description: 'Bold experimental fusion with unexpected elements',
      icon: '🚀'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedPersona || !userPrompt.trim()) return;

    onGenerateOutfit({
      persona_id: selectedPersona.id,
      user_prompt: userPrompt.trim(),
      temperature: temperature,
      gender: gender
    });
  };

  const promptSuggestions = [
    'Create a futuristic outfit with layered textures',
    'Design something for a lunar tide ritual ceremony',
    'Make an outfit for a digital fashion runway show',
    'Create a look for exploring virtual worlds',
    'Design something that blends ancient and future aesthetics'
  ];

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Gender Selection */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-white">
            Avatar Gender
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="gender"
                value="female"
                checked={gender === 'female'}
                onChange={(e) => setGender(e.target.value)}
                className="h-4 w-4 text-blue-400 focus:ring-blue-400 border-gray-500 bg-gray-700"
                disabled={disabled}
              />
              <span className="ml-2 text-sm text-white">Female</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="gender"
                value="male"
                checked={gender === 'male'}
                onChange={(e) => setGender(e.target.value)}
                className="h-4 w-4 text-blue-400 focus:ring-blue-400 border-gray-500 bg-gray-700"
                disabled={disabled}
              />
              <span className="ml-2 text-sm text-white">Male</span>
            </label>
          </div>
        </div>

        {/* Text Prompt Input */}
        <div className="space-y-3">
          <label htmlFor="prompt" className="block text-sm font-medium text-white">
            Outfit Description
          </label>
          <textarea
            id="prompt"
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            placeholder="Describe the outfit you want to create..."
            className="w-full h-32 px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent resize-none"
            disabled={disabled || !selectedPersona}
          />

          {/* Prompt Suggestions */}
          <div className="space-y-2">
            <p className="text-sm text-gray-300">Try these suggestions:</p>
            <div className="flex flex-wrap gap-2">
              {promptSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setUserPrompt(suggestion)}
                  className="px-3 py-1 text-xs bg-gray-700/50 text-gray-300 rounded-full hover:bg-gray-600/50 transition-colors border border-gray-600/50"
                  disabled={disabled || !selectedPersona}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Creativity Temperature */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-white">
            Creativity Level
          </label>
          <div className="grid grid-cols-1 gap-3">
            {temperatureOptions.map((option) => (
              <label
                key={option.value}
                className={`
                  relative cursor-pointer transition-all duration-200 flex items-center p-4 rounded-lg border
                  ${temperature === option.value
                    ? 'ring-2 ring-blue-400 bg-blue-500/20 border-blue-400'
                    : 'hover:bg-gray-700/30 border-gray-600/50'
                  }
                  bg-gray-800/30 border-gray-600/50
                  ${disabled || !selectedPersona ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <input
                  type="radio"
                  name="temperature"
                  value={option.value}
                  checked={temperature === option.value}
                  onChange={(e) => setTemperature(e.target.value)}
                  className="h-4 w-4 text-blue-400 focus:ring-blue-400 border-gray-500 bg-gray-700 mr-4"
                  disabled={disabled || !selectedPersona}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{option.icon}</span>
                    <span className="font-medium text-white">{option.label}</span>
                  </div>
                  <div className="text-sm text-gray-300 mt-1">{option.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Generate Button */}
        <button
          type="submit"
          disabled={disabled || !selectedPersona || !userPrompt.trim() || loading}
          className={`
            w-full py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200
            ${disabled || !selectedPersona || !userPrompt.trim()
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : loading
              ? 'bg-blue-600 text-white cursor-wait'
              : 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25'
            }
          `}
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <span>AI is Creating Your Style...</span>
            </div>
          ) : (
            'Visualize My Style ✨'
          )}
        </button>
      </form>
    </div>
  );
};

export default StylingInputs;
