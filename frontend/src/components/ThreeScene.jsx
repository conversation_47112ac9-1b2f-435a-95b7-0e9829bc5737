import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import assetLoader from '../services/assetLoader';

// Avatar component with enhanced outfit visualization
const Avatar = ({ outfitTags }) => {
  const meshRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);

  // Process outfit tags when they change
  useEffect(() => {
    if (outfitTags) {
      assetLoader.processOutfitTags(outfitTags).then(setProcessedAssets);
    } else {
      setProcessedAssets(null);
    }
  }, [outfitTags]);

  // Rotate the avatar slowly
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
    }
  });

  return (
    <group ref={meshRef}>
      {/* Placeholder avatar - a simple humanoid shape */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[0.8, 2, 0.4]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>

      {/* Head */}
      <mesh position={[0, 1.3, 0]}>
        <sphereGeometry args={[0.3]} />
        <meshStandardMaterial color="#A78BFA" />
      </mesh>

      {/* Arms */}
      <mesh position={[-0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1.2, 0.2]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>
      <mesh position={[0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1.2, 0.2]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>

      {/* Legs */}
      <mesh position={[-0.2, -1.5, 0]}>
        <boxGeometry args={[0.25, 1, 0.25]} />
        <meshStandardMaterial color="#7C3AED" />
      </mesh>
      <mesh position={[0.2, -1.5, 0]}>
        <boxGeometry args={[0.25, 1, 0.25]} />
        <meshStandardMaterial color="#7C3AED" />
      </mesh>

      {/* Enhanced outfit visualization */}
      {processedAssets && (
        <OutfitVisualization processedAssets={processedAssets} />
      )}
    </group>
  );
};

// Component to visualize outfit based on processed assets
const OutfitVisualization = ({ processedAssets }) => {
  return (
    <group>
      {/* Garment overlays */}
      {processedAssets.garments?.map((garment, index) => (
        <GarmentOverlay
          key={index}
          garmentData={garment}
          position={[0, 0, 0.05 + index * 0.01]}
        />
      ))}

      {/* Accessories */}
      {processedAssets.accessories?.map((accessory, index) => (
        <AccessoryOverlay
          key={index}
          accessoryData={accessory}
          index={index}
        />
      ))}

      {/* Visual effects for vibes */}
      {processedAssets.vibes?.length > 0 && (
        <VibeEffects vibes={processedAssets.vibes} />
      )}
    </group>
  );
};

// Enhanced garment overlay visualization
const GarmentOverlay = ({ garmentData, position }) => {
  const { tag, fallback_color, category } = garmentData;

  // Adjust geometry based on garment category
  const getGarmentGeometry = (category) => {
    switch (category) {
      case 'bodysuit':
        return <boxGeometry args={[0.85, 2.1, 0.08]} />;
      case 'gown':
        return <boxGeometry args={[1.2, 2.5, 0.1]} />;
      case 'top':
        return <boxGeometry args={[0.9, 1.2, 0.1]} />;
      case 'bottom':
        return <boxGeometry args={[0.8, 1.0, 0.1]} />;
      case 'robe':
        return <boxGeometry args={[1.1, 2.3, 0.12]} />;
      case 'outerwear':
        return <boxGeometry args={[0.95, 1.4, 0.12]} />;
      default:
        return <boxGeometry args={[0.85, 2.1, 0.1]} />;
    }
  };

  // Adjust position based on category
  const getGarmentPosition = (category, basePosition) => {
    const [x, y, z] = basePosition;
    switch (category) {
      case 'top':
        return [x, y + 0.5, z];
      case 'bottom':
        return [x, y - 0.8, z];
      case 'outerwear':
        return [x, y + 0.3, z + 0.02];
      default:
        return basePosition;
    }
  };

  return (
    <mesh position={getGarmentPosition(category, position)}>
      {getGarmentGeometry(category)}
      <meshStandardMaterial
        color={fallback_color}
        transparent
        opacity={0.8}
        roughness={0.4}
        metalness={0.2}
      />
    </mesh>
  );
};

// Enhanced accessory visualization
const AccessoryOverlay = ({ accessoryData, index }) => {
  const { tag, fallback_color, position, attachment_point } = accessoryData;

  // Get accessory geometry based on type
  const getAccessoryGeometry = (tag) => {
    if (tag.includes('Halo') || tag.includes('Headpiece')) {
      return <torusGeometry args={[0.25, 0.05, 8, 16]} />;
    }
    if (tag.includes('Belt') || tag.includes('Obi')) {
      return <boxGeometry args={[0.9, 0.1, 0.05]} />;
    }
    if (tag.includes('Neckpiece') || tag.includes('Collar')) {
      return <torusGeometry args={[0.2, 0.03, 8, 16]} />;
    }
    if (tag.includes('Earrings')) {
      return <sphereGeometry args={[0.03]} />;
    }
    return <sphereGeometry args={[0.08]} />; // Default
  };

  // Handle special positioning for paired accessories
  const getAccessoryPositions = (tag, basePosition) => {
    if (tag.includes('Earrings')) {
      return [
        [-0.25, basePosition[1], basePosition[2]], // Left ear
        [0.25, basePosition[1], basePosition[2]]   // Right ear
      ];
    }
    if (tag.includes('Gauntlets')) {
      return [
        [-0.6, basePosition[1], basePosition[2]], // Left hand
        [0.6, basePosition[1], basePosition[2]]   // Right hand
      ];
    }
    return [basePosition]; // Single accessory
  };

  const positions = getAccessoryPositions(tag, position);

  return (
    <group>
      {positions.map((pos, idx) => (
        <mesh key={idx} position={pos}>
          {getAccessoryGeometry(tag)}
          <meshStandardMaterial
            color={fallback_color}
            metalness={0.7}
            roughness={0.3}
          />
        </mesh>
      ))}
    </group>
  );
};

// Vibe effects component for atmospheric enhancement
const VibeEffects = ({ vibes }) => {
  const effectsRef = useRef();

  useFrame((state) => {
    if (effectsRef.current) {
      // Subtle pulsing effect based on vibes
      const intensity = 0.5 + Math.sin(state.clock.elapsedTime * 2) * 0.2;
      effectsRef.current.children.forEach((child, index) => {
        if (child.material) {
          child.material.opacity = intensity * (0.1 + index * 0.05);
        }
      });
    }
  });

  // Create ambient effects based on vibe tags
  const getVibeEffects = () => {
    const effects = [];

    vibes.forEach((vibe, index) => {
      let color = '#8B5CF6'; // Default purple

      if (vibe.includes('Afrofuturist')) color = '#F59E0B';
      if (vibe.includes('TripHop')) color = '#3B82F6';
      if (vibe.includes('Digital')) color = '#06B6D4';
      if (vibe.includes('Ritual')) color = '#EC4899';

      effects.push(
        <mesh key={index} position={[0, 0, -0.5 - index * 0.1]}>
          <sphereGeometry args={[2 + index * 0.5, 16, 16]} />
          <meshStandardMaterial
            color={color}
            transparent
            opacity={0.05}
            side={2} // DoubleSide
          />
        </mesh>
      );
    });

    return effects;
  };

  return (
    <group ref={effectsRef}>
      {getVibeEffects()}
    </group>
  );
};

// Loading component
const LoadingSpinner = () => {
  const meshRef = useRef();

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.x += 0.02;
      meshRef.current.rotation.y += 0.02;
    }
  });

  return (
    <mesh ref={meshRef}>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshStandardMaterial color="#8B5CF6" wireframe />
    </mesh>
  );
};

// Main ThreeScene component
const ThreeScene = ({ outfitTags, loading }) => {
  return (
    <div className="w-full h-full min-h-[500px] bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={0.8} />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#8B5CF6" />

        {/* Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          minDistance={3}
          maxDistance={8}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
        />

        {/* Scene content */}
        <Suspense fallback={<LoadingSpinner />}>
          {loading ? (
            <LoadingSpinner />
          ) : (
            <Avatar outfitTags={outfitTags} />
          )}
        </Suspense>

        {/* Ground plane */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2.5, 0]}>
          <planeGeometry args={[10, 10]} />
          <meshStandardMaterial color="#1F2937" transparent opacity={0.3} />
        </mesh>
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
          <p className="text-sm font-medium">3D Viewer</p>
          <p className="text-xs text-gray-300">Drag to rotate • Scroll to zoom</p>
        </div>
      </div>
    </div>
  );
};

export default ThreeScene;