import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';

// Avatar component - placeholder for now
const Avatar = ({ outfitTags }) => {
  const meshRef = useRef();

  // Rotate the avatar slowly
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
    }
  });

  return (
    <group ref={meshRef}>
      {/* Placeholder avatar - a simple humanoid shape */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[0.8, 2, 0.4]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>

      {/* Head */}
      <mesh position={[0, 1.3, 0]}>
        <sphereGeometry args={[0.3]} />
        <meshStandardMaterial color="#A78BFA" />
      </mesh>

      {/* Arms */}
      <mesh position={[-0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1.2, 0.2]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>
      <mesh position={[0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1.2, 0.2]} />
        <meshStandardMaterial color="#8B5CF6" />
      </mesh>

      {/* Legs */}
      <mesh position={[-0.2, -1.5, 0]}>
        <boxGeometry args={[0.25, 1, 0.25]} />
        <meshStandardMaterial color="#7C3AED" />
      </mesh>
      <mesh position={[0.2, -1.5, 0]}>
        <boxGeometry args={[0.25, 1, 0.25]} />
        <meshStandardMaterial color="#7C3AED" />
      </mesh>

      {/* Outfit visualization - simple overlays for now */}
      {outfitTags && (
        <OutfitVisualization outfitTags={outfitTags} />
      )}
    </group>
  );
};

// Component to visualize outfit based on AI tags
const OutfitVisualization = ({ outfitTags }) => {
  const { garment_components_tags, texture_shader_tags, accessory_tags, vibe_tags } = outfitTags;

  return (
    <group>
      {/* Garment overlays */}
      {garment_components_tags?.map((garment, index) => (
        <GarmentOverlay key={index} garmentTag={garment} position={[0, 0, 0.05]} />
      ))}

      {/* Accessories */}
      {accessory_tags?.map((accessory, index) => (
        <AccessoryOverlay key={index} accessoryTag={accessory} index={index} />
      ))}
    </group>
  );
};

// Simple garment overlay visualization
const GarmentOverlay = ({ garmentTag, position }) => {
  // Color mapping based on garment type
  const getGarmentColor = (tag) => {
    if (tag.includes('Bodysuit')) return '#EC4899'; // Pink
    if (tag.includes('Gown')) return '#8B5CF6'; // Purple
    if (tag.includes('Hoodie')) return '#3B82F6'; // Blue
    if (tag.includes('Pants')) return '#10B981'; // Green
    return '#F59E0B'; // Orange default
  };

  return (
    <mesh position={position}>
      <boxGeometry args={[0.85, 2.1, 0.1]} />
      <meshStandardMaterial
        color={getGarmentColor(garmentTag)}
        transparent
        opacity={0.7}
        roughness={0.3}
        metalness={0.1}
      />
    </mesh>
  );
};

// Simple accessory visualization
const AccessoryOverlay = ({ accessoryTag, index }) => {
  const getAccessoryPosition = (tag, index) => {
    if (tag.includes('Headpiece') || tag.includes('Halo')) return [0, 1.6, 0.3];
    if (tag.includes('Neckpiece') || tag.includes('Collar')) return [0, 0.8, 0.3];
    if (tag.includes('Belt') || tag.includes('Obi')) return [0, -0.2, 0.3];
    if (tag.includes('Gauntlets')) return [0.6, 0.3, 0.3];
    return [0.3 * (index + 1), 0.5, 0.3]; // Default positioning
  };

  const getAccessoryColor = (tag) => {
    if (tag.includes('Gold')) return '#F59E0B';
    if (tag.includes('Tech')) return '#06B6D4';
    if (tag.includes('Neural')) return '#8B5CF6';
    return '#EF4444'; // Red default
  };

  return (
    <mesh position={getAccessoryPosition(accessoryTag, index)}>
      <sphereGeometry args={[0.1]} />
      <meshStandardMaterial
        color={getAccessoryColor(accessoryTag)}
        metalness={0.8}
        roughness={0.2}
      />
    </mesh>
  );
};

// Loading component
const LoadingSpinner = () => {
  const meshRef = useRef();

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.x += 0.02;
      meshRef.current.rotation.y += 0.02;
    }
  });

  return (
    <mesh ref={meshRef}>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshStandardMaterial color="#8B5CF6" wireframe />
    </mesh>
  );
};

// Main ThreeScene component
const ThreeScene = ({ outfitTags, loading }) => {
  return (
    <div className="w-full h-full min-h-[500px] bg-gradient-to-b from-gray-900 to-black rounded-lg overflow-hidden">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight position={[10, 10, 5]} intensity={0.8} />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#8B5CF6" />

        {/* Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          minDistance={3}
          maxDistance={8}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
        />

        {/* Scene content */}
        <Suspense fallback={<LoadingSpinner />}>
          {loading ? (
            <LoadingSpinner />
          ) : (
            <Avatar outfitTags={outfitTags} />
          )}
        </Suspense>

        {/* Ground plane */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2.5, 0]}>
          <planeGeometry args={[10, 10]} />
          <meshStandardMaterial color="#1F2937" transparent opacity={0.3} />
        </mesh>
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 text-white">
        <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
          <p className="text-sm font-medium">3D Viewer</p>
          <p className="text-xs text-gray-300">Drag to rotate • Scroll to zoom</p>
        </div>
      </div>
    </div>
  );
};

export default ThreeScene;