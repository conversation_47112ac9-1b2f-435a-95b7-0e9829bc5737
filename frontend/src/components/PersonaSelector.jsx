import React from 'react';

const PersonaSelector = ({ personas, selectedPersona, onPersonaSelect, loading }) => {
  // Simple persona data for MVP
  const mockPersonas = [
    {
      id: 'signature-style',
      name: 'Signature Style',
      description: 'Classic interpretation of the persona\'s aesthetic DNA',
      color: '#4F46E5'
    },
    {
      id: 'creative-wildcard',
      name: 'Creative Wildcard',
      description: 'Bold experimental fusion with unexpected elements',
      color: '#7C3AED'
    }
  ];

  // Transform string personas from API into objects
  const transformPersonas = (personaList) => {
    if (!personaList || personaList.length === 0) return mockPersonas;

    return personaList.map((persona, index) => {
      // If it's already an object, return as is
      if (typeof persona === 'object' && persona.name) {
        return persona;
      }

      // If it's a string, transform it into an object with correct backend IDs
      const colors = ['#4F46E5', '#7C3AED', '#059669', '#DC2626', '#7C2D12'];

      // Map persona names to their backend IDs
      const personaIdMap = {
        'Maya': 'maya_001',
        'Dr<PERSON> <PERSON><PERSON>': 'dr_diallo_002',
        '<PERSON>': 'kai_<PERSON><PERSON><PERSON>_003'
      };

      const personaName = typeof persona === 'string' ? persona : `Persona ${index + 1}`;
      const personaId = personaIdMap[personaName] || personaName.toLowerCase().replace(/\s+/g, '-');

      return {
        id: personaId,
        name: personaName,
        description: `Unique style interpretation for ${personaName}`,
        color: colors[index % colors.length]
      };
    });
  };

  const displayPersonas = transformPersonas(personas);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <span className="ml-3 text-white">Loading personas...</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {displayPersonas.map((persona) => (
        <button
          key={persona.id}
          onClick={() => onPersonaSelect(persona)}
          className={`
            group relative p-8 rounded-2xl transition-all duration-300 text-left
            ${(selectedPersona?.id === persona.id || selectedPersona?.name === persona.name)
              ? 'bg-white/25 border-2 border-white/50 shadow-xl scale-105'
              : 'bg-white/15 border border-white/25 hover:bg-white/20 hover:border-white/40 hover:scale-102'
            }
            backdrop-blur-md transform hover:shadow-2xl
          `}
        >
          <div className="text-center">
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4 shadow-lg"
              style={{ backgroundColor: persona.color || '#4F46E5' }}
            >
              {persona.name.charAt(0)}
            </div>
            <div>
              <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-100 transition-colors">
                {persona.name}
              </h3>
              <p className="text-blue-100 text-sm leading-relaxed opacity-90">
                {persona.description}
              </p>
            </div>
          </div>

          {/* Selection indicator */}
          {(selectedPersona?.id === persona.id || selectedPersona?.name === persona.name) && (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-2xl pointer-events-none" />
              <div className="absolute top-3 right-3 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </>
          )}
        </button>
      ))}
    </div>
  );
};

export default PersonaSelector;
