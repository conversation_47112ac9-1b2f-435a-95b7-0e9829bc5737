import React from 'react';

const PersonaSelector = ({ personas, selectedPersona, onPersonaSelect, loading }) => {
  // Mock persona data for MVP
  const mockPersonas = [
    {
      id: 'maya_001',
      name: '<PERSON>',
      pronouns: 'they/them',
      profession: 'Digital Fashion Designer, 3D Generalist, Freelancer',
      style_summary: 'Afrofuturist silhouettes, digital textiles with surreal shaders, ravewear meets ritual',
      primary_vibes: ['Afrofuturist', 'TripHop', 'Digital Textiles', 'Ritual Tech']
    },
    {
      id: 'dr_diallo_002',
      name: 'Dr. <PERSON><PERSON>',
      pronouns: 'she/her',
      profession: 'Digital Fashion Researcher, Cultural Technologist',
      style_summary: 'Neo-ancestral glamour, tech-enhanced traditional silhouettes, regal futurism',
      primary_vibes: ['Neo-Ancestral', 'Regal', 'Tech-Enhanced', 'Cultural Fusion']
    },
    {
      id: 'kai_003',
      name: '<PERSON>',
      pronouns: 'he/him',
      profession: 'Sustainable Fashion Technologist, Eco-Designer',
      style_summary: 'Solarpunk utility, bio-mythic aesthetics, ancestral tech fusion',
      primary_vibes: ['Solarpunk', 'Bio-Mythic', 'Ancestral Tech', 'Eco-Luminescent']
    }
  ];

  // Process personas data
  const processPersonas = (apiPersonas) => {
    if (!apiPersonas || apiPersonas.length === 0) return mockPersonas;

    // If API returns just names, map them to our mock data
    if (typeof apiPersonas[0] === 'string') {
      return apiPersonas.map(name => {
        const mockPersona = mockPersonas.find(p => p.name === name);
        return mockPersona || {
          id: name.toLowerCase().replace(/\s+/g, '_'),
          name: name,
          pronouns: '',
          profession: 'Digital Fashion Designer',
          style_summary: 'Unique digital fashion aesthetic',
          primary_vibes: ['Digital', 'Fashion', 'Creative']
        };
      });
    }

    return apiPersonas;
  };

  const displayPersonas = processPersonas(personas);

  if (loading) {
    return (
      <div className="w-full max-w-4xl mx-auto">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">
          Choose Your Creative Persona
        </h2>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-white mb-6 text-center">
        Choose Your Creative Persona
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {displayPersonas.map((persona) => (
          <div
            key={persona.id}
            onClick={() => onPersonaSelect(persona)}
            className={`
              relative cursor-pointer transition-all duration-300 transform hover:scale-105
              ${selectedPersona?.id === persona.id
                ? 'ring-2 ring-purple-400 shadow-lg shadow-purple-400/25'
                : 'hover:ring-1 hover:ring-purple-300/50'
              }
              bg-gradient-to-br from-gray-800/80 to-gray-900/80
              backdrop-blur-sm rounded-xl p-6 border border-gray-700/50
            `}
          >
            {/* Selection indicator */}
            {selectedPersona?.id === persona.id && (
              <div className="absolute top-3 right-3">
                <div className="w-6 h-6 bg-purple-400 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            )}

            {/* Avatar placeholder */}
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-2xl font-bold text-white">
                {persona.name?.charAt(0) || '?'}
              </span>
            </div>

            {/* Persona info */}
            <div className="text-center">
              <h3 className="text-xl font-semibold text-white mb-1">
                {persona.name || 'Unknown Persona'}
              </h3>
              <p className="text-sm text-gray-300 mb-2">
                {persona.pronouns || ''}
              </p>
              <p className="text-sm text-gray-400 mb-3">
                {persona.profession || 'Digital Fashion Designer'}
              </p>
              <p className="text-xs text-gray-300 mb-4 overflow-hidden" style={{
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical'
              }}>
                {persona.style_summary || 'Unique digital fashion aesthetic'}
              </p>

              {/* Vibe tags */}
              <div className="flex flex-wrap gap-1 justify-center">
                {persona.primary_vibes?.slice(0, 3).map((vibe, index) => (
                  <span
                    key={`${persona.id}-vibe-${index}`}
                    className="px-2 py-1 text-xs bg-purple-500/20 text-purple-300 rounded-full border border-purple-500/30"
                  >
                    {vibe}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PersonaSelector;
