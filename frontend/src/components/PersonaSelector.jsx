import React from 'react';

const PersonaSelector = ({ personas, selectedPersona, onPersonaSelect, loading }) => {
  // Simple persona data for MVP
  const mockPersonas = [
    {
      id: 'signature-style',
      name: 'Signature Style',
      description: 'Classic interpretation of the persona\'s aesthetic DNA',
      color: '#4F46E5'
    },
    {
      id: 'creative-wildcard',
      name: 'Creative Wildcard',
      description: 'Bold experimental fusion with unexpected elements',
      color: '#7C3AED'
    }
  ];

  const displayPersonas = personas.length > 0 ? personas : mockPersonas;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <span className="ml-3 text-white">Loading personas...</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {displayPersonas.map((persona) => (
        <button
          key={persona.id}
          onClick={() => onPersonaSelect(persona)}
          className={`
            group relative p-6 rounded-xl transition-all duration-300 text-left
            ${selectedPersona?.id === persona.id
              ? 'bg-white/20 border-2 border-white/40 shadow-lg'
              : 'bg-white/10 border border-white/20 hover:bg-white/15 hover:border-white/30'
            }
            backdrop-blur-sm transform hover:scale-105
          `}
        >
          <div className="flex items-center space-x-4">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0"
              style={{ backgroundColor: persona.color || '#4F46E5' }}
            >
              {persona.name.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-blue-200 transition-colors">
                {persona.name}
              </h3>
              <p className="text-blue-200 text-sm leading-relaxed">
                {persona.description}
              </p>
            </div>
          </div>

          {selectedPersona?.id === persona.id && (
            <div className="absolute inset-0 bg-blue-400/10 rounded-xl pointer-events-none" />
          )}
        </button>
      ))}
    </div>
  );
};

export default PersonaSelector;
