import React, { useState, useEffect } from 'react';
import PersonaSelector from './components/PersonaSelector';
import StylingInputs from './components/StylingInputs';
import ThreeScene from './components/ThreeScene.jsx';
import apiService from './services/apiService';

const App = () => {
  // State management
  const [selectedPersona, setSelectedPersona] = useState(null);
  const [personas, setPersonas] = useState([]);
  const [outfitTags, setOutfitTags] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [personasLoading, setPersonasLoading] = useState(true);

  // Load personas on component mount
  useEffect(() => {
    const loadPersonas = async () => {
      try {
        setPersonasLoading(true);
        const response = await apiService.getPersonas();
        setPersonas(response.personas || []);
      } catch (err) {
        console.error('Failed to load personas:', err);
        // Continue with mock data if API fails
      } finally {
        setPersonasLoading(false);
      }
    };

    loadPersonas();
  }, []);

  // Handle persona selection
  const handlePersonaSelect = (persona) => {
    setSelectedPersona(persona);
    setError(null);
    // Clear previous outfit when switching personas
    setOutfitTags(null);
  };

  // Handle outfit generation
  const handleGenerateOutfit = async (stylingRequest) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Generating outfit with request:', stylingRequest);

      const response = await apiService.generateOutfit(stylingRequest);
      setOutfitTags(response);

      console.log('Generated outfit tags:', response);
    } catch (err) {
      console.error('Failed to generate outfit:', err);
      setError(err.message || 'Failed to generate outfit. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-2">
              AI Stylist MVP
            </h1>
            <p className="text-lg text-gray-300">
              Your Creative Co-Pilot for Digital Fashion Design
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Controls */}
          <div className="space-y-8">
            {/* Persona Selection */}
            <section>
              <PersonaSelector
                personas={personas}
                selectedPersona={selectedPersona}
                onPersonaSelect={handlePersonaSelect}
                loading={personasLoading}
              />
            </section>

            {/* Styling Inputs */}
            <section>
              <StylingInputs
                selectedPersona={selectedPersona}
                onGenerateOutfit={handleGenerateOutfit}
                loading={loading}
                disabled={!selectedPersona}
              />
            </section>

            {/* Error Display */}
            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              </div>
            )}

            {/* AI Output Tags Display (Debug/Demo) */}
            {outfitTags && (
              <section className="bg-gray-800/30 border border-gray-600/30 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-3">
                  AI Generated Tags
                </h3>
                <div className="space-y-2 text-sm">
                  {Object.entries(outfitTags).map(([key, value]) => {
                    if (key === 'metadata') return null;
                    return (
                      <div key={key} className="flex flex-wrap gap-1">
                        <span className="text-purple-300 font-medium min-w-0">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <div className="flex flex-wrap gap-1">
                          {Array.isArray(value) ? (
                            value.map((item, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-purple-500/20 text-purple-200 rounded text-xs"
                              >
                                {item}
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-300">{value}</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </section>
            )}
          </div>

          {/* Right Panel - 3D Viewer */}
          <div className="lg:sticky lg:top-8">
            <section className="bg-gray-800/20 border border-gray-600/30 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4 text-center">
                3D Visualization
              </h3>
              <div className="relative">
                <ThreeScene
                  outfitTags={outfitTags}
                  loading={loading}
                />

                {/* Placeholder message when no outfit is generated */}
                {!outfitTags && !loading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50 rounded-lg">
                    <div className="text-center text-gray-400">
                      <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 4h6V3H9v1zm-4 3v9h14V7H5z" />
                      </svg>
                      <p className="text-lg font-medium">Select a persona and create your style</p>
                      <p className="text-sm">Your AI-generated outfit will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </section>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm border-t border-white/10 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-gray-400 text-sm">
            <p>AI Stylist MVP - Digital Fashion Co-Creation Tool</p>
            <p className="mt-1">Built for The Fabricant Partnership Demo</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default App;