import React, { useState, useEffect } from 'react';
import PersonaSelector from './components/PersonaSelector';
import StylingInputs from './components/StylingInputs';
import ThreeScene from './components/ThreeScene.jsx';
import apiService from './services/apiService';

const App = () => {
  // State management
  const [selectedPersona, setSelectedPersona] = useState(null);
  const [personas, setPersonas] = useState([]);
  const [outfitTags, setOutfitTags] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [personasLoading, setPersonasLoading] = useState(true);

  // Load personas on component mount
  useEffect(() => {
    const loadPersonas = async () => {
      try {
        setPersonasLoading(true);
        console.log('🔄 Loading personas from API...');
        const response = await apiService.getPersonas();
        console.log('✅ Personas API response:', response);
        const personasData = response.personas || [];
        console.log('📋 Setting personas data:', personasData);
        setPersonas(personasData);
      } catch (err) {
        console.error('❌ Failed to load personas:', err);
        // Continue with mock data if API fails - PersonaSelector will handle this
        console.log('📋 Setting empty personas array, PersonaSelector will use mock data');
        setPersonas([]);
      } finally {
        setPersonasLoading(false);
      }
    };

    loadPersonas();
  }, []);

  // Handle persona selection
  const handlePersonaSelect = (persona) => {
    setSelectedPersona(persona);
    setError(null);
    setOutfitTags(null);
  };

  // Handle outfit generation
  const handleGenerateOutfit = async (stylingRequest) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Generating outfit with request:', stylingRequest);

      const response = await apiService.generateOutfit(stylingRequest);
      setOutfitTags(response);

      console.log('Generated outfit tags:', response);
    } catch (err) {
      console.error('Failed to generate outfit:', err);
      setError(err.message || 'Failed to generate outfit. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-2">
              <span className="bg-gradient-to-r from-blue-200 to-purple-200 bg-clip-text text-transparent">
                AI Stylist MVP
              </span>
            </h1>
            <p className="text-lg text-blue-200">
              Digital Fashion Co-Creation Tool
            </p>
            <p className="text-sm text-blue-300 mt-1">
              Built for The Fabricant Partnership Demo
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Controls */}
          <div className="space-y-6">
            {/* Persona Selection */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Choose Your Creative Direction</h3>
              <PersonaSelector
                personas={personas}
                selectedPersona={selectedPersona}
                onPersonaSelect={handlePersonaSelect}
                loading={personasLoading}
              />
            </div>

            {/* Styling Controls */}
            {selectedPersona && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Style Configuration</h3>
                <StylingInputs
                  selectedPersona={selectedPersona}
                  onGenerateOutfit={handleGenerateOutfit}
                  loading={loading}
                  disabled={!selectedPersona}
                />
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              </div>
            )}

            {/* AI Generated Tags Display */}
            {outfitTags && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">AI Generated Tags</h3>
                <div className="space-y-3">
                  {Object.entries(outfitTags).map(([key, value]) => {
                    if (key === 'metadata') return null;
                    return (
                      <div key={key} className="border-b border-white/10 pb-3 last:border-b-0 last:pb-0">
                        <h4 className="text-sm font-medium text-blue-200 mb-2 capitalize">
                          {key.replace(/_/g, ' ')}
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {Array.isArray(value) ? (
                            value.map((item, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-blue-500/20 text-blue-200 rounded-full text-xs font-medium"
                              >
                                {item}
                              </span>
                            ))
                          ) : (
                            <span className="text-blue-100 text-sm">{value}</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - 3D Avatar Display */}
          <div className="lg:sticky lg:top-8">
            <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-lg border border-white/20 overflow-hidden">
              {/* 3D Viewer Header */}
              <div className="bg-white/90 backdrop-blur-sm border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">3D Avatar</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span>Drag to rotate • Scroll to zoom</span>
                  </div>
                </div>
              </div>

              {/* 3D Viewer Content */}
              <div className="relative" style={{ height: '600px' }}>
                <ThreeScene
                  outfitTags={outfitTags}
                  loading={loading}
                />

                {/* Empty State */}
                {!selectedPersona && !loading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <svg className="w-20 h-20 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <h4 className="text-lg font-medium text-gray-700 mb-2">Choose a Persona</h4>
                      <p className="text-gray-500">Select a creative direction to begin styling</p>
                    </div>
                  </div>
                )}

                {/* Persona Selected but No Outfit */}
                {selectedPersona && !outfitTags && !loading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <svg className="w-20 h-20 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V7a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 4h6V3H9v1zm-4 3v9h14V7H5z" />
                      </svg>
                      <h4 className="text-lg font-medium text-gray-700 mb-2">Ready for Styling</h4>
                      <p className="text-gray-500">Configure your style and click "Visualize My Style"</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;