import React, { useState, useEffect } from 'react';
import LandingPage from './components/LandingPage';
import StylistPage from './components/StylistPage';
import apiService from './services/apiService';

const App = () => {
  // State management
  const [currentPage, setCurrentPage] = useState('landing'); // 'landing' or 'stylist'
  const [selectedPersona, setSelectedPersona] = useState(null);
  const [personas, setPersonas] = useState([]);
  const [outfitTags, setOutfitTags] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [personasLoading, setPersonasLoading] = useState(true);

  // Load personas on component mount
  useEffect(() => {
    const loadPersonas = async () => {
      try {
        setPersonasLoading(true);
        console.log('🔄 Loading personas from API...');
        const response = await apiService.getPersonas();
        console.log('✅ Personas API response:', response);
        const personasData = response.personas || [];
        console.log('📋 Setting personas data:', personasData);
        setPersonas(personasData);
      } catch (err) {
        console.error('❌ Failed to load personas:', err);
        // Continue with mock data if API fails - PersonaSelector will handle this
        console.log('📋 Setting empty personas array, PersonaSelector will use mock data');
        setPersonas([]);
      } finally {
        setPersonasLoading(false);
      }
    };

    loadPersonas();
  }, []);

  // Handle persona selection and navigation
  const handlePersonaSelect = (persona) => {
    setSelectedPersona(persona);
    setCurrentPage('stylist');
    setError(null);
    setOutfitTags(null);
  };

  // Handle outfit generation
  const handleGenerateOutfit = async (stylingRequest) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Generating outfit with request:', stylingRequest);

      const response = await apiService.generateOutfit(stylingRequest);
      setOutfitTags(response);

      console.log('Generated outfit tags:', response);
    } catch (err) {
      console.error('Failed to generate outfit:', err);
      setError(err.message || 'Failed to generate outfit. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle back to landing
  const handleBackToLanding = () => {
    setCurrentPage('landing');
    setSelectedPersona(null);
    setOutfitTags(null);
    setError(null);
  };

  if (currentPage === 'landing') {
    return (
      <LandingPage
        personas={personas}
        onPersonaSelect={handlePersonaSelect}
        loading={personasLoading}
        error={error}
      />
    );
  }

  return (
    <StylistPage
      selectedPersona={selectedPersona}
      outfitTags={outfitTags}
      onGenerateOutfit={handleGenerateOutfit}
      onBackToLanding={handleBackToLanding}
      loading={loading}
      error={error}
    />
  );
};

export default App;