// API Service Module for AI Stylist Frontend
// Handles all communication with the FastAPI backend

const API_BASE_URL = 'http://localhost:8000';

class ApiService {
  // Helper method for making HTTP requests
  async makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Health check endpoint
  async healthCheck() {
    return this.makeRequest('/health');
  }

  // Get all personas
  async getPersonas() {
    return this.makeRequest('/personas');
  }

  // Get specific persona by ID
  async getPersonaById(personaId) {
    return this.makeRequest(`/personas/${personaId}`);
  }

  // Main AI styling endpoint
  async generateOutfit(stylingRequest) {
    const { persona_id, user_prompt, temperature } = stylingRequest;
    
    // Validate required fields
    if (!persona_id || !user_prompt || !temperature) {
      throw new Error('Missing required fields: persona_id, user_prompt, and temperature are required');
    }

    return this.makeRequest('/style_me', {
      method: 'POST',
      body: JSON.stringify({
        persona_id,
        user_prompt,
        temperature,
      }),
    });
  }

  // Rate outfit endpoint (for future implementation)
  async rateOutfit(outfitId, rating) {
    return this.makeRequest('/rate_outfit', {
      method: 'POST',
      body: JSON.stringify({
        outfit_identifier: outfitId,
        rating_value: rating,
      }),
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
