// Asset Loader Service for AI Stylist MVP
// Handles loading and mapping of 3D assets based on AI tags

class AssetLoader {
  constructor() {
    this.manifest = null;
    this.loadedAssets = new Map();
    this.loadingPromises = new Map();
  }

  // Load the asset manifest
  async loadManifest() {
    if (this.manifest) return this.manifest;

    try {
      const response = await fetch('/asset_manifest.json');
      if (!response.ok) {
        throw new Error(`Failed to load asset manifest: ${response.status}`);
      }
      this.manifest = await response.json();
      console.log('✅ Asset manifest loaded:', this.manifest);
      return this.manifest;
    } catch (error) {
      console.error('❌ Failed to load asset manifest:', error);
      // Return a minimal fallback manifest
      this.manifest = {
        avatars: {},
        garments: {},
        accessories: {},
        textures: {}
      };
      return this.manifest;
    }
  }

  // Get asset info by tag
  getAssetInfo(category, tag) {
    if (!this.manifest) {
      console.warn('Asset manifest not loaded');
      return null;
    }

    const categoryData = this.manifest[category];
    if (!categoryData) {
      console.warn(`Category '${category}' not found in manifest`);
      return null;
    }

    const assetInfo = categoryData[tag];
    if (!assetInfo) {
      console.warn(`Asset '${tag}' not found in category '${category}'`);
      return null;
    }

    return assetInfo;
  }

  // Get garment info with fallback
  getGarmentInfo(tag) {
    const info = this.getAssetInfo('garments', tag);
    if (info) return info;

    // Return fallback info for unknown garments
    return {
      path: null,
      fallback_color: this.getGarmentFallbackColor(tag),
      category: this.inferGarmentCategory(tag),
      description: `Fallback for ${tag}`
    };
  }

  // Get accessory info with fallback
  getAccessoryInfo(tag) {
    const info = this.getAssetInfo('accessories', tag);
    if (info) return info;

    // Return fallback info for unknown accessories
    return {
      path: null,
      fallback_color: this.getAccessoryFallbackColor(tag),
      attachment_point: this.inferAttachmentPoint(tag),
      position: this.getDefaultPosition(tag),
      description: `Fallback for ${tag}`
    };
  }

  // Get texture info with fallback
  getTextureInfo(tag) {
    const info = this.getAssetInfo('textures', tag);
    if (info) return info;

    // Return fallback material properties
    return {
      type: 'color_hex',
      value: this.getTextureFallbackColor(tag),
      metallic: 0.2,
      roughness: 0.7,
      description: `Fallback for ${tag}`
    };
  }

  // Fallback color logic for garments
  getGarmentFallbackColor(tag) {
    if (tag.includes('Bodysuit')) return '#EC4899';
    if (tag.includes('Gown')) return '#8B5CF6';
    if (tag.includes('Hoodie')) return '#3B82F6';
    if (tag.includes('Pants')) return '#10B981';
    if (tag.includes('Agbada')) return '#F59E0B';
    return '#6B7280'; // Default gray
  }

  // Fallback color logic for accessories
  getAccessoryFallbackColor(tag) {
    if (tag.includes('Gold')) return '#F59E0B';
    if (tag.includes('Tech')) return '#06B6D4';
    if (tag.includes('Neural')) return '#8B5CF6';
    if (tag.includes('Tribal')) return '#EF4444';
    if (tag.includes('Luminescent')) return '#10B981';
    return '#9CA3AF'; // Default light gray
  }

  // Fallback color logic for textures
  getTextureFallbackColor(tag) {
    if (tag.includes('Metal')) return '#C0C0C0';
    if (tag.includes('Gold')) return '#F59E0B';
    if (tag.includes('Liquid')) return '#06B6D4';
    if (tag.includes('Nanofiber')) return '#EC4899';
    if (tag.includes('Smart')) return '#8B5CF6';
    return '#6B7280'; // Default gray
  }

  // Infer garment category from tag
  inferGarmentCategory(tag) {
    if (tag.includes('Bodysuit')) return 'bodysuit';
    if (tag.includes('Gown') || tag.includes('Dress')) return 'gown';
    if (tag.includes('Hoodie') || tag.includes('Top')) return 'top';
    if (tag.includes('Pants') || tag.includes('Leggings')) return 'bottom';
    if (tag.includes('Agbada') || tag.includes('Robe')) return 'robe';
    if (tag.includes('Jumpsuit')) return 'jumpsuit';
    if (tag.includes('Jacket') || tag.includes('Capelet')) return 'outerwear';
    return 'unknown';
  }

  // Infer attachment point from tag
  inferAttachmentPoint(tag) {
    if (tag.includes('Headpiece') || tag.includes('Halo')) return 'head';
    if (tag.includes('Neckpiece') || tag.includes('Collar') || tag.includes('Pendant')) return 'neck';
    if (tag.includes('Belt') || tag.includes('Obi')) return 'waist';
    if (tag.includes('Gauntlets')) return 'hands';
    if (tag.includes('Bangles')) return 'wrists';
    if (tag.includes('Earrings')) return 'ears';
    return 'body';
  }

  // Get default position for attachment point
  getDefaultPosition(tag) {
    const point = this.inferAttachmentPoint(tag);
    switch (point) {
      case 'head': return [0, 1.6, 0.3];
      case 'neck': return [0, 0.8, 0.3];
      case 'waist': return [0, -0.2, 0.3];
      case 'hands': return [0.6, 0.3, 0.3];
      case 'wrists': return [0.4, 0.1, 0.3];
      case 'ears': return [0.25, 1.4, 0.2];
      default: return [0, 0, 0.3];
    }
  }

  // Process outfit tags and return structured asset data
  async processOutfitTags(outfitTags) {
    await this.loadManifest();

    const result = {
      garments: [],
      accessories: [],
      textures: [],
      colors: [],
      vibes: outfitTags.vibe_tags || []
    };

    // Process garment components
    if (outfitTags.garment_components_tags) {
      for (const tag of outfitTags.garment_components_tags) {
        const garmentInfo = this.getGarmentInfo(tag);
        result.garments.push({
          tag,
          ...garmentInfo
        });
      }
    }

    // Process accessories
    if (outfitTags.accessory_tags) {
      for (const tag of outfitTags.accessory_tags) {
        const accessoryInfo = this.getAccessoryInfo(tag);
        result.accessories.push({
          tag,
          ...accessoryInfo
        });
      }
    }

    // Process textures
    if (outfitTags.texture_shader_tags) {
      for (const tag of outfitTags.texture_shader_tags) {
        const textureInfo = this.getTextureInfo(tag);
        result.textures.push({
          tag,
          ...textureInfo
        });
      }
    }

    // Process colors
    if (outfitTags.color_palette_tags_applied) {
      result.colors = outfitTags.color_palette_tags_applied;
    }

    console.log('🎨 Processed outfit tags:', result);
    return result;
  }

  // Get avatar model path
  getAvatarPath(gender = 'female') {
    if (!this.manifest) return null;
    
    const avatars = this.manifest.avatars;
    if (gender === 'female' && avatars.default_female) {
      return avatars.default_female;
    }
    if (gender === 'male' && avatars.default_male) {
      return avatars.default_male;
    }
    
    // Return first available avatar as fallback
    const firstAvatar = Object.values(avatars)[0];
    return firstAvatar || null;
  }
}

// Export singleton instance
export const assetLoader = new AssetLoader();
export default assetLoader;
